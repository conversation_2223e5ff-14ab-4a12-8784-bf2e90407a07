import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import ChatBotProvider from "./components/ChatBotProvider";
import ScrollToTop from "@/components/ScrollToTop"; // Import the ScrollToTop component


// Admin pages
import Login from "./pages/admin/Login";
import Dashboard from "./pages/admin/Dashboard";
import PopupManager from "./pages/admin/PopupManager";
import UserSubmissions from "./pages/admin/UserSubmissions";
import CreateAdmin from "./pages/admin/CreateAdmin";
import TestFirestore from "./pages/admin/TestFirestore";
import DiagnosticPage from "./pages/admin/DiagnosticPage";
import ChatDetails from "./pages/admin/ChatDetails";
import TestPopupData from "./components/TestPopupData";

// Main category pages
import Measure from "./pages/Measure";
import Protect from "./pages/Protect";
import Conserve from "./pages/Conserve";
import About from "./pages/About";
import Contact from "./pages/Contact";

// Contact subpages
import Sales from "./pages/contact/Sales";
import Service from "./pages/contact/Service";


// Measure subpages
import PowerQualityAnalyzers from "./pages/measure/PowerQualityAnalyzers";
import InsulationTesters from "./pages/measure/InsulationTesters";
import Oscilloscopes from "./pages/measure/Oscilloscopes";
import EarthLoopTesters from "./pages/measure/EarthLoopTester";
import DigitalMultimeters from "./pages/measure/Multimeters";
import ClampMeters from "./pages/measure/clampmeters";
import EarthTesters from "./pages/measure/EarthTesters";
import MicroOhmmeters from "./pages/measure/MicroOhmmeters";
import MultiFunctionalMeters from "./pages/measure/MultiFunctionalMeters";
import ThermalImagers from "./pages/measure/ThermalImager";
import InstallationTesters from "./pages/measure/InstallationTesters";

import ThermalImagersSpecification from "./pages/measure/productpages/ThermalImagersSpecification";
import PowerQualityAnalyzerDetail from "./pages/measure/productpages/PowerQualityAnalyzerDetail";

// Protect subpages
import UPS from "./pages/protect/UPS";
import ServoStabilizers from "./pages/protect/ServoStabilizers";
import StaticStabilizers from "./pages/protect/StaticStabilizers";
import IsolationTransformers from "./pages/protect/IsolationTransformers";
import IsolationTransformersProduct from "./pages/protect/productpages/isolationtransformersproduct";
import VoltageRegulatorProduct from "./pages/protect/productpages/Staticstabilizersproduct";
import SinglePhaseStabilizer from "./pages/protect/productpages/Servostabilizersphase1";
import ThreePhaseStabilizer from "./pages/protect/productpages/Servostabilizers3phase";

// Conserve subpages
import OnPremiseSystems from "./pages/conserve/OnPremiseSystems";
import CloudEnergyManagement from "./pages/conserve/CloudEnergyManagement";
import LightingEnergySaver from "./pages/conserve/LightingEnergySaver";


// About subpages
import Company from "./pages/about/Company";
import Sustainability from "./pages/about/Sustainability";
import Events from "./pages/about/events";
// import Partners from "./pages/about/Partners";
// import Resources from "./pages/about/Resources";


// New UPS Series Product Specification Pages
import ELSeriesUPS from "./pages/protect/productpages/EL/ELBSeriesUPS"; // EL/ELB Series
import EH11SeriesUPS from "./pages/protect/productpages/EH11SeriesUPS"; // EH 11 Series
import EH31SeriesUPS from "./pages/protect/productpages/EH31SeriesUPS"; // EH 31 Series
import EH33SmallSeriesUPS from "./pages/protect/productpages/EH33SmallSeriesUPS"; // EH 33 Series (10-60 kVA)
import EH33LargeSeriesUPS from "./pages/protect/productpages/EH33LargeSeriesUPS"; // EH 33 Series (80-200 kVA)
import SXSeriesUPS from "./pages/protect/productpages/SXSeriesUPS"; // SX Series
import HXSeriesUPS from "./pages/protect/productpages/HXSeriesUPS"; // HX Series
import ErrorBoundary from "./components/ErrorBoundary";
import EventPopup from "./components/EventPopup";
import { AuthProvider } from "./contexts/AuthContext";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <ScrollToTop /> {/* Add ScrollToTop component here */}
        <EventPopup />
        <AuthProvider >
        <ChatBotProvider>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* Admin Routes */}
              <Route path="/admin/login" element={<Login />} />
              <Route path="/admin/create" element={<CreateAdmin />} />
              <Route path="/admin/test-firestore" element={<TestFirestore />} />
              <Route path="/admin/diagnostic" element={<DiagnosticPage />} />
              <Route path="/admin/test-popup" element={<TestPopupData />} />
              <Route path="/admin" element={<Dashboard />}>
                <Route path="dashboard" element={<PopupManager />} />
                <Route path="popups" element={<PopupManager />} />
                <Route path="users" element={<UserSubmissions />} />
                <Route path="chat/:id" element={<ChatDetails />} />
              </Route>

            {/* Main category pages */}
            <Route path="/measure" element={<Measure />} />
            <Route path="/protect" element={<Protect />} />
            <Route path="/conserve" element={<Conserve />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />

            {/* Contact subpages */}
            <Route path="/contact/sales" element={<Sales />} />
            <Route path="/contact/service" element={<Service />} />

            {/* Measure subpages */}
            <Route path="/measure/power-quality-analyzers" element={<PowerQualityAnalyzers />} />
            <Route path="/measure/thermal-imagers" element={<ThermalImagers />} />
            <Route path="/measure/insulation-testers" element={<InsulationTesters />} />
            <Route path="/measure/earth-loop-testers" element={<EarthLoopTesters />} />
            <Route path="/measure/digital-multimeters" element={<DigitalMultimeters />} />
            <Route path="/measure/clamp-meters" element={<ClampMeters />} />
            <Route path="/measure/earthtesters" element={<EarthTesters />} />
            <Route path="/measure/micro-ohmmeters" element={<MicroOhmmeters />} />
            <Route path="/measure/multi-functional-meters" element={<MultiFunctionalMeters />} />
            <Route path="/measure/thermal-imagers" element={<ThermalImagers />} />
            <Route path="/measure/installation-testers" element={<InstallationTesters />} />
            <Route path="/measure/oscilloscopes" element={<Oscilloscopes />} />

            <Route path="/measure/productpages/thermal-imagers/specification" element={<ThermalImagersSpecification />} />
            <Route path="/measure/productpages/power-quality-analyzers/:productId" element={<PowerQualityAnalyzerDetail />} />

            {/* Protect subpages */}
            <Route path="/protect/ups" element={<UPS />} />
            <Route path="/protect/servo-stabilizers" element={<ServoStabilizers />} />
            <Route path="/protect/productpages/SinglePhaseStabilizer" element={<SinglePhaseStabilizer />} />
            <Route path="/protect/productpages/ThreePhaseStabilizer" element={<ThreePhaseStabilizer />} />
            <Route path="/protect/static-stabilizers" element={<StaticStabilizers />} />
            <Route path="/protect/static-stabilizers/product" element={<VoltageRegulatorProduct />} />
            <Route path="/protect/isolation-transformers" element={<IsolationTransformers />} />
            <Route path="/protect/isolation-transformers/:type" element={<IsolationTransformersProduct />} />

            {/* UPS Series Pages - Using the new dedicated product pages */}
            <Route path="/protect/ups/el-series" element={<ELSeriesUPS />} />
            <Route path="/protect/ups/eh-11-series" element={<EH11SeriesUPS />} />
            <Route path="/protect/ups/eh-31-series" element={<EH31SeriesUPS />} />
            <Route path="/protect/ups/eh-33-small-series" element={<EH33SmallSeriesUPS />} />
            <Route path="/protect/ups/eh-33-large-series" element={<EH33LargeSeriesUPS />} />
            <Route path="/protect/ups/sx-series" element={<SXSeriesUPS />} />
            <Route path="/protect/ups/hx-series" element={<HXSeriesUPS />} />

            {/* Conserve subpages */}
            <Route path="/conserve/on-premise-systems" element={<OnPremiseSystems />} />
            <Route path="/conserve/cloud-energy-management" element={<CloudEnergyManagement />} />
            <Route path="/conserve/lighting-energy-saver" element={<LightingEnergySaver />} />

            {/* About subpages */}
            <Route path="/about/company" element={<Company />} />
            <Route path="/about/sustainability" element={<Sustainability />} />
            <Route path="/about/events" element={<Events />} />
            {/* <Route path="/about/partners" element={<Partners />} />
            <Route path="/about/resources" element={<Resources />} /> */}

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </ChatBotProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
  </ErrorBoundary>
);

export default App;