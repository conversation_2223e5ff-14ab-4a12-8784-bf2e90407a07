import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Shield, Settings, Volume2, Award, CheckCircle, Star, Info, Mail, Download } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

export default function TransformerProductPage() {
  const { type } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Map URL parameter to product title
  const getTransformerTitle = (urlType: string | undefined) => {
    switch(urlType) {
      case 'ultra-isolation-transformer':
        return 'Ultra Isolation Transformer';
      case 'galvanic-isolation-transformer':
        return 'Galvanic Isolation Transformer';
      case 'auto-transformer':
        return 'Auto Transformer';
      default:
        return 'Isolation Transformer';
    }
  };

  useEffect(() => {
    const init = async () => {
      try {
        // Start with loading state
        setIsLoading(true);

        // Validate the type parameter
        const validTypes = ['ultra-isolation-transformer', 'galvanic-isolation-transformer', 'auto-transformer'];
        if (!type || !validTypes.includes(type)) {
          setError('Invalid transformer type');
          navigate('/protect/isolation-transformers');
          return;
        }

        // Reset any previous errors
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    init();
  }, [type, navigate]);

  const transformerTitle = getTransformerTitle(type);

  // If loading, show loading state immediately
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center font-sans">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-blue-600 mb-2">Loading...</h2>
          <p className="text-lg text-blue-500">Please wait while we load the transformer details</p>
        </div>
      </div>
    );
  }

  // If there's an error, show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center font-sans">
        <div className="text-center">
          <div className="text-red-600 mb-6">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-lg text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  const specifications = [
    { parameter: 'Ratings', '3Phase': '3 kVA to 1000 kVA', '1Phase': '1 kVA to 25 kVA' },
    { parameter: 'Reference Standard', '3Phase': 'IS 11171 : 1985 (Reaffirmed 2006)', '1Phase': '-' },
    { parameter: 'Type of Transformer', '3Phase': 'Floor mounted, natural air-cooled / oil-cooled depending on rating', '1Phase': 'Floor mounted, natural air-cooled' },
    { parameter: 'Configuration', '3Phase': 'Delta / star, LT or as per user specification', '1Phase': 'LT or as per user specification' },
    { parameter: 'Default Vector Group', '3Phase': 'Dyn11', '1Phase': '-' },
    { parameter: 'Type of Insulation', '3Phase': 'CRGO', '1Phase': '-' },
    { parameter: 'Winding', '3Phase': 'Copper Wire / Strip depending on rating wire / strip', '1Phase': '-' },
    { parameter: 'Load Regulation', '3Phase': 'Better than 3%', '1Phase': '-' },
    { parameter: 'Class of Insulation', '3Phase': 'H', '1Phase': '-' },
    { parameter: 'Efficiency', '3Phase': '≥ 97 kVA (>95%)', '1Phase': '-' },
    { parameter: 'Insulation Strength', '3Phase': 'AC rated input voltage and 50 Hz for 1 minute for all coils that are linear', '1Phase': '-' },
    { parameter: 'BIS Withstand', '3Phase': 'Withstands 2.5 kV for 1 minute (between windings & between windings and body)', '1Phase': '-' },
    { parameter: 'DC galvanic isolation', '3Phase': '> 1000 Mega Ohms - for LIP', '1Phase': '-' },
    { parameter: 'Common Mode Noise Rejection (for UIT only)', '3Phase': '> 100 Mega Ohms - for UIT', '1Phase': '-' },
    { parameter: 'Short Circuit Protection', '3Phase': 'Up to 10Hz 2 < 100 dB at 50 Hz for 60 dB', '1Phase': '-' },
    { parameter: 'Indications', '3Phase': 'HRC Fuse at 440 V system as a standard / MCB / MCCB protection is also an option', '1Phase': '-' },
    { parameter: 'Housing', '3Phase': 'LED Lamps For Output Presence Digital Voltmeter (DVM) - optional', '1Phase': 'Sheet metal housing provided with Input / Output terminations' }
  ];

  const features = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Wide Product Range",
      items: [
        "Step Up / Step Down Auto Transformers",
        "Galvanic Isolation Transformers",
        "Shielded Ultra Isolation Transformers",
        "K Rated Ultra Isolation Transformers",
        "Ratings 3 kVA to 1000 kVA 3 Phase and 1 kVA to 25 kVA 1 Phase / 2 Phase"
      ]
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Compliance to Standards, Efficiency & Reliability",
      items: [
        "Meets IS 11171 1985 Dry Type transformer standards",
        "Efficient designs using Class H Insulation",
        "Low impedance and temperature rise"
      ]
    },
    {
      icon: <Settings className="w-6 h-6" />,
      title: "Multiple add-on options",
      items: [
        "Multi-tap options available",
        "MCB / MCCB for Short Circuit protection",
        "RFI / EMI Filters",
        "TVSS / SPD for Transient protection",
        "Voltage / Current / Power Metering"
      ]
    },
    {
      icon: <Volume2 className="w-6 h-6" />,
      title: "Noise Attenuation",
      items: [
        "Good Transverse Mode noise attenuation",
        "Double Shielded for high Common Mode Noise Rejection (Ultra Isolation Transformers)"
      ]
    }
  ];
  return (
    <PageLayout
      title={transformerTitle}
      subtitle="High-quality power solutions for your needs"
      category="protect"
    >
      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16">
        {/* Hero Content Section - Integrated with PageLayout */}
        <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white relative">
          {/* Background Image with Transparency */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
            style={{
              backgroundImage: "url('https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
            }}
          ></div>
          <div className="w-full px-6 md:px-12 lg:px-16 py-16 md:py-24 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-8">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Advanced Power Solutions
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                  Premium quality transformers designed for safety, reliability, and efficiency across various industrial applications. Our advanced power solutions provide superior power quality and protection for critical systems.
                </p>
                <div className="flex items-center justify-center gap-6">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-7 h-7 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-4 text-xl text-blue-100 font-medium">Industry Leading Quality</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-6 justify-center pt-4">
                  <button
                    onClick={() => {
                      // Redirect to Sales page for quote
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Mail className="w-6 h-6 inline mr-3" />
                    Get Quote
                  </button>
                  <button
                    onClick={() => {
                      // Create a link to download the PDF brochure
                      const link = document.createElement('a');
                      link.href = '/brochures/isolation-transformers-brochure.pdf';
                      link.download = 'Isolation-Transformers-Brochure.pdf';
                      link.click();
                    }}
                    className="bg-white text-blue-800 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Download className="w-6 h-6 inline mr-3" />
                    Download Brochure
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Power Solutions
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Isolation & Auto Transformers provide superior power quality, safety, and reliability for critical applications across industries. Built to meet international standards with cutting-edge technology, these transformers ensure optimal performance and protection for your valuable equipment and systems.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all applications.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      3-Phase
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      1-Phase / 2-Phase
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['3Phase']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['1Phase']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per IS 11171:1985 standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-100 to-blue-200">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Key Features & Benefits
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Discover the advanced features that make our transformers the preferred choice for critical power applications. Each feature is designed to deliver maximum performance, safety, and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-2xl p-10 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-2 border-blue-300">
                <div className="flex items-center mb-8">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-5 shadow-xl">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 ml-6">{feature.title}</h3>
                </div>
                <div className="space-y-5">
                  {feature.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-4 mt-1">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <span className="text-lg text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Ready to Power Your Operations?
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Get in touch with our experts to find the perfect transformer solution for your needs. We provide comprehensive support from consultation to installation and maintenance.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
}