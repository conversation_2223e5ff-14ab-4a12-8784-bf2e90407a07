import React, { useRef } from "react";
import { motion } from "framer-motion";
import {
  ArrowR<PERSON>,
  FileText,
  Mail
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";
import { useNavigate } from "react-router-dom";

// Watermark Component
const Watermark = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 flex items-center justify-center overflow-hidden">
      <div className="absolute w-full h-full">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="watermark" patternUnits="userSpaceOnUse" width="600" height="600">
              <text
                x="300"
                y="300"
                fontSize="60"
                fontWeight="bold"
                fill="rgba(59, 130, 246, 0.03)"
                textAnchor="middle"
                dominantBaseline="middle"
                transform="rotate(-45, 300, 300)"
              >
                ISOLATION TRANSFORMER
              </text>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#watermark)" />
        </svg>
      </div>
    </div>
  );
};

// Main component
const IsolationTransformers = () => {
  const heroRef = useRef(null);
  const advantagesRef = useRef(null);
  const navigate = useNavigate();

  // Function to handle View Details click
  const handleViewDetails = (transformerType: string) => {
    navigate(`/protect/isolation-transformers/${transformerType.toLowerCase().replace(/\s+/g, '-')}`);
  };

  // Function to open brochure PDF
  const openBrochure = () => {
    // URL to your PDF file
    const pdfUrl = "/KRYKARD-Isolation-Transformer-Brochure.pdf";

    // Open PDF directly in a new tab
    window.open(pdfUrl, '_blank');
  };

  const advantageItems = [
    {
      title: "Better Heat Transfer",
      description: "Improved thermal management for longer transformer life",
      icon: "🔄"
    },
    {
      title: "Lower Temperature Rise",
      description: "Reduced operating temperatures for enhanced reliability",
      icon: "🌡️"
    },
    {
      title: "Lower Weight",
      description: "Lightweight design for easier installation and handling",
      icon: "⚖️"
    },
    {
      title: "Better Surge with Standing Capacity",
      description: "Enhanced protection against power surges",
      icon: "⚡"
    },
    {
      title: "Better Suited for Harmonics",
      description: "Optimized performance with harmonic-rich loads",
      icon: "〰️"
    },
    {
      title: "No Hot-spots",
      description: "Even thermal distribution prevents damaging hot spots",
      icon: "🎯"
    },
    {
      title: "More Efficient",
      description: "Higher energy efficiency saving operational costs",
      icon: "📈"
    }
  ];

  const transformerTypes = [
    {
      title: "Ultra Isolation Transformer",
      description: "A double wound transformer with multiple shielding for superior noise rejection, making it ideal for sensitive equipment.",
      features: [
        "Low inter-winding capacitance",
        "Special RF galvanic isolation",
        "High attenuation (>80 dB) of common mode up to 10 MHz",
        "Optimum transformer design ensures good load regulation"
      ],
      accentColor: "from-blue-600 to-indigo-600",
      textColor: "text-blue-700",
      bgColor: "bg-gradient-to-br from-blue-50 to-indigo-50"
    },
    {
      title: "Galvanic Isolation Transformer",
      description: "A double wound transformer with KRYKARD Advantage, which provides galvanic isolation and hence addresses problems related to input neutral.",
      features: [
        "Complete galvanic isolation between input & output",
        "Copper shield between primary & secondary windings",
        "Low capacitive coupling between windings",
        "Custom voltage configurations available",
        "High surge immunity protection",
        "Suitable for medical and sensitive equipment"
      ],
      accentColor: "from-blue-500 to-cyan-600",
      textColor: "text-blue-600",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50"
    },
    {
      title: "Auto Transformer",
      description: "A single wound transformer with KRYKARD Advantage, designed to change the output voltage (step-up or step-down) without isolation to suit different machine requirements.",
      features: [
        "Compact and lightweight design",
        "Excellent voltage regulation",
        "High efficiency (>98%)",
        "Step-up or step-down functionality",
        "Cost-effective solution for voltage adaptation",
        "Available in various capacity ranges"
      ],
      accentColor: "from-indigo-500 to-blue-600",
      textColor: "text-indigo-700",
      bgColor: "bg-gradient-to-br from-indigo-50 to-blue-50"
    }
  ];

  const specifications = [
    {
      category: "Input",
      items: [
        { label: "Voltage", value: "150-270V AC" },
        { label: "Frequency", value: "50Hz ±5%" },
        { label: "Phase", value: "Single Phase" }
      ]
    },
    {
      category: "Output",
      items: [
        { label: "Voltage Range", value: "Programmable 160-240V AC" },
        { label: "Regulation", value: "±1%" },
        { label: "Protection", value: "Overload, Short Circuit" }
      ]
    },
    {
      category: "Mechanical",
      items: [
        { label: "Enclosure", value: "CRCA Powder Coated" },
        { label: "Protection Class", value: "IP54" },
        { label: "Operating Temperature", value: "0-45°C" }
      ]
    }
  ];

  const applications = [
    { text: "Medical Equipment & Healthcare", icon: "🏥" },
    { text: "Data Centers & IT Infrastructure", icon: "🖥️" },
    { text: "Industrial Automation Systems", icon: "🏭" },
    { text: "Laboratories & Research Facilities", icon: "🔬" },
    { text: "Sensitive Measuring Equipment", icon: "📊" }
  ];

  return (
    <PageLayout
      title="Isolation Transformers"
      subtitle="Electrical separation for noise reduction and safety"
      category="protect"
    >
      {/* Watermark */}
      <Watermark />      {/* Enhanced Hero Section with ServoStabilizers style */}
      <section
        id="overview"
        ref={heroRef}
        className="py-12 md:py-16 lg:py-20 mb-0 relative overflow-hidden"
      >
        {/* Enhanced colorful background */}
        <div className="absolute inset-0 -z-10 overflow-hidden">          {/* Main gradient background - pure blue */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-blue-50 to-blue-100 dark:from-blue-900/40 dark:via-blue-800/40 dark:to-blue-900/40"></div>          {/* Animated color blobs - pure blue */}
          <motion.div
            className="absolute -top-20 -right-20 w-96 h-96 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute -bottom-40 -left-20 w-96 h-96 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute top-1/3 right-1/4 w-64 h-64 rounded-full bg-blue-300/15 blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              x: [0, 30, 0],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          {/* Subtle pattern overlay */}
          <div className="absolute inset-0 opacity-5 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.1)_1px,transparent_0)]" style={{ backgroundSize: "24px 24px" }}></div>
        </div>

        <div className="container mx-auto px-4">          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-10 lg:gap-16 items-center">
            {/* Content on the left */}
            <motion.div
              className="lg:col-span-6"
            >
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, ease: "easeOut" }}
                className="relative"
              >
                {/* Decorative element */}
                <div className="absolute -left-6 -top-6 w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-indigo-300 opacity-20 blur-2xl"></div>

                <h1 className="relative mb-8">
                  {/* Main title with enhanced styling */}
                  <div className="flex flex-col items-start">                  <motion.span                      className="text-3xl md:text-4xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 dark:from-blue-400 dark:via-blue-300 dark:to-blue-400 leading-none mb-2"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.7, ease: "easeOut" }}
                    >
                      ISOLATION
                    </motion.span>
                    <motion.div
                      className="flex items-center"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.7, delay: 0.1, ease: "easeOut" }}
                    >
                      <span className="text-3xl md:text-4xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 dark:from-blue-400 dark:via-blue-300 dark:to-blue-400 leading-none">
                        TRANSFORMERS
                      </span>
                    </motion.div>
                  </div>

                  {/* Decorative elements */}                  <div className="absolute -left-10 -top-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
                  <motion.div
                    className="absolute -right-4 bottom-0 w-24 h-24 bg-gradient-to-br from-blue-500/10 to-blue-500/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  ></motion.div>

                  {/* Animated underline */}                  <motion.div
                    className="absolute -bottom-2 left-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: "60%" }}
                    transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
                  ></motion.div>
                </h1>

                <motion.p                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
                  className="text-slate-900 dark:text-slate-200 text-lg md:text-xl mb-8 leading-relaxed font-medium"
                >
                  Advanced power conditioning technology with
                  <span className="text-blue-600 dark:text-blue-400 font-semibold"> unparalleled electrical isolation </span>
                  for sensitive equipment and critical applications.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}                  className="mt-6"
                >
                  <h3 className="text-xl md:text-2xl font-bold mb-6 text-gray-900">Perfect for:</h3>
                  <motion.ul className="space-y-3 md:space-y-5 pl-2">
                    {applications.map((app, index) => (
                      <motion.li
                        key={index}
                        className="flex items-center group"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                      >
                        <span className="text-2xl md:text-3xl mr-3 md:mr-4 transform group-hover:scale-110 transition-transform text-blue-600">
                          {app.icon}
                        </span>
                        <span className="text-gray-900 font-semibold group-hover:text-blue-600 transition-colors">
                          {app.text}
                        </span>
                      </motion.li>
                    ))}
                  </motion.ul>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}                  className="flex flex-wrap gap-3 md:gap-6 mt-8"
                >
                  <Button
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg font-semibold text-sm md:text-base px-4 md:px-8 py-4 md:py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1"
                    style={{ boxShadow: "0 10px 30px -5px rgba(59, 130, 246, 0.5)" }}
                    onClick={() => window.location.href = "/contact/sales"}
                  >
                    <span className="mr-2">GET A QUOTE</span>
                    <ArrowRight className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>

                  {/* Brochure Button - Opens PDF directly */}
                  <Button
                    variant="outline"
                    className="border-2 border-blue-600 text-blue-700 hover:bg-blue-50 hover:text-blue-800 hover:border-blue-800 px-4 md:px-8 py-4 md:py-6 rounded-xl transition-all duration-300 flex items-center gap-2 shadow-lg transform hover:-translate-y-1"
                    onClick={openBrochure}
                  >
                    <FileText className="h-5 w-5" />
                    <span>VIEW BROCHURE</span>
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Image moved to the right */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-6 relative"
            >
              {/* Clean image display without box layout or tags */}
              <motion.div
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="relative"
              >                <img
                  src="/isolation-banner-1.png"
                  alt="KRYKARD Isolation Transformer"
                  className="w-full h-auto object-contain drop-shadow-xl transform scale-110"
                />
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>      {/* Enhanced Premium Features Section with 3D-like cards and interactive elements */}
      <section id="features" ref={advantagesRef} className="py-16 md:py-24 lg:py-32 relative overflow-hidden">        {/* Enhanced colorful background with depth - pure blue */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background with more vibrant colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/80 via-blue-50 to-blue-100/80 dark:from-blue-900/40 dark:via-blue-800/40 dark:to-blue-900/40"></div>

          {/* Animated floating elements */}
          <motion.div
            className="absolute top-20 right-[15%] w-72 h-72 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              y: [0, -40, 0],
              scale: [1, 1.1, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-40 left-[10%] w-80 h-80 rounded-full bg-blue-300/20 blur-3xl"
            animate={{
              y: [0, 50, 0],
              scale: [1.1, 1, 1.1],
              opacity: [0.2, 0.25, 0.2],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />          {/* Enhanced pattern overlay - blue only */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: "radial-gradient(circle at 20px 20px, rgba(59, 130, 246, 0.3) 2px, transparent 0)",
              backgroundSize: "24px 24px"
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-center mb-20"
          >            <motion.h2              className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 dark:from-blue-400 dark:via-blue-300 dark:to-blue-400 inline-block mb-6"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              PREMIUM FEATURES
            </motion.h2>

            <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-blue-600 mx-auto mt-4 rounded-full"></div>            <motion.p
              className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-8 text-lg md:text-xl font-medium leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Our transformers feature unique technological advantages that
              <span className="text-blue-600 font-semibold"> set them apart </span>
              from conventional designs, delivering
              <span className="text-blue-600 font-semibold"> unmatched performance</span>.
            </motion.p>
          </motion.div>          {/* Feature cards in a grid layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto">
            {advantageItems.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: index * 0.15 }}
                whileHover={{
                  y: -10,
                  transition: { type: "spring", stiffness: 300, damping: 20 }
                }}
                className="group relative"
              >{/* Card shadow/glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                {/* Main card */}                <div className="bg-white dark:bg-slate-800 rounded-xl shadow-xl p-4 md:p-8 border border-blue-100/80 dark:border-blue-900/30 relative z-10 h-full transform transition-all duration-500 group-hover:shadow-2xl">
                  <div className="flex flex-col items-center text-center"><motion.div
                      className="mb-5 w-16 h-16 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-lg text-white transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-3xl">{item.icon}</span>
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 mb-3">{item.title}</h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 text-center">{item.description}</p>

                  {/* Decorative corner accent */}
                  <div className="absolute -bottom-2 -right-2 w-20 h-20 border-r-2 border-b-2 border-blue-400/30 rounded-br-xl opacity-40"></div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Bottom decorative element */}
          <motion.div
            className="mt-20 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="inline-flex items-center justify-center">
              <div className="h-px w-12 bg-blue-300 dark:bg-blue-700"></div>
              <div className="mx-4 text-blue-500 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div className="h-px w-12 bg-blue-300 dark:bg-blue-700"></div>
            </div>
          </motion.div>
        </div>
      </section>      {/* Modern Product Types Section with clean card layout based on example image */}
      <section id="products" className="py-20 md:py-24 lg:py-32 relative overflow-hidden bg-white dark:bg-slate-900">{/* Light background with subtle patterns - blue only */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main background color */}
          <div className="absolute inset-0 bg-blue-50 dark:bg-blue-900"></div>
          
          {/* Subtle pattern overlay */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: "radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.2) 2px, transparent 0)",
              backgroundSize: "30px 30px"
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >            <motion.h2              className="relative inline-block mb-8"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >              <span className="text-3xl md:text-4xl lg:text-5xl font-black tracking-tight bg-gradient-to-r from-blue-900 via-blue-700 to-blue-800 bg-clip-text text-transparent drop-shadow-md">
                Isolation Transformer Types
              </span>
              <motion.div 
                className="absolute -z-10 inset-0 bg-blue-400/10 blur-xl rounded-full"
                animate={{
                  scale: [0.95, 1.05, 0.95],
                  opacity: [0.3, 0.5, 0.3]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <motion.div 
                className="absolute -bottom-2 left-0 h-1.5 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full w-3/4 mx-auto"
                initial={{ width: "0%" }}
                whileInView={{ width: "60%" }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.7, ease: "easeOut" }}
              />
            </motion.h2>            <motion.p
              className="text-center text-gray-900 dark:text-gray-100 max-w-3xl mx-auto mt-4 text-lg font-medium"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Explore our range of high-performance isolation solutions tailored for different applications
            </motion.p>
          </motion.div>          {/* Enhanced Product Cards - Unique 3D-style layout with interactive elements */}
          <div className="max-w-6xl mx-auto space-y-16">
            {transformerTypes.map((transformer, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: index * 0.1 }}
                className="relative"
              >
                {/* Floating orbs in background for decoration */}
                <div className="absolute -z-10 inset-0">                  <motion.div 
                    className="absolute w-24 h-24 rounded-full bg-blue-400/10 blur-xl"
                    animate={{
                      x: [0, 10, 0],
                      y: [0, -10, 0],
                      scale: [1, 1.1, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                    style={{
                      top: "20%",
                      left: "5%",
                    }}
                  />
                  <motion.div 
                    className="absolute w-20 h-20 rounded-full bg-blue-400/10 blur-xl"
                    animate={{
                      x: [0, -10, 0],
                      y: [0, 10, 0],
                      scale: [1, 1.2, 1],
                      opacity: [0.2, 0.4, 0.2],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                    style={{
                      bottom: "10%",
                      right: "10%",
                    }}
                  /></div>                {/* Modern clean card design */}                <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg overflow-hidden border border-gray-100/60 dark:border-slate-700/60 transition-all duration-300 hover:shadow-xl">                  <div className={`flex flex-col md:flex-row md:min-h-[auto] lg:min-h-[600px] ${index === 0 || index === 2 ? 'md:flex-row-reverse' : ''}`}>
                    {/* Content section - positioned based on card index */}                    <div className="md:w-1/2 p-4 sm:p-6 md:p-10">
                      {/* Modern clean title */}
                      <div className="mb-6">                        <div className="flex items-center mb-5">                          <motion.div 
                            className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-4 shadow-md"
                            whileHover={{ 
                              scale: 1.1, 
                              rotate: 5,
                              boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)"
                            }}
                            transition={{ type: "spring", stiffness: 300 }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </motion.div>
                          <div className="flex flex-col">                            <motion.span
                              className="text-xs uppercase tracking-wider text-blue-800 dark:text-blue-300 font-bold mb-1"
                              initial={{ opacity: 0, x: -10 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              viewport={{ once: true }}
                              transition={{ duration: 0.5 }}
                            >
                              Premium Series
                            </motion.span>                              <motion.h3 
                              className="text-2xl md:text-3xl font-extrabold bg-gradient-to-r from-blue-900 via-blue-700 to-blue-800 dark:from-blue-500 dark:via-blue-300 to-blue-400 bg-clip-text text-transparent relative"
                              initial={{ opacity: 0, y: 10 }}
                              whileInView={{ opacity: 1, y: 0 }}
                              viewport={{ once: true }}
                              transition={{ duration: 0.5, delay: 0.1 }}
                            >
                              {transformer.title}
                              <span className="absolute -bottom-2 left-0 w-1/3 h-0.5 bg-gradient-to-r from-blue-600 to-blue-600"></span>
                            </motion.h3>
                          </div>
                        </div>
                      </div>
                      
                      {/* Modern description */}                      <p className="text-gray-900 dark:text-gray-100 text-base leading-relaxed mb-7">
                        {transformer.description}
                      </p>
                      
                      {/* Key Features with enhanced list */}
                      <div className="mb-8">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                          <span className="inline-block mr-2 w-1 h-5 bg-blue-500 rounded-full"></span>
                          Key Features
                        </h4>
                        <div className="pl-1">
                          <ul className="space-y-3">                            {transformer.features.map((feature, fidx) => (
                              <li 
                                key={fidx} 
                                className="flex items-start group"
                              >
                                <div className="text-blue-500 dark:text-blue-400 mr-3 mt-0.5 bg-blue-50 dark:bg-blue-900/20 p-1 rounded-md transition-colors group-hover:bg-blue-100 dark:group-hover:bg-blue-900/40">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                                <span className="text-gray-900 dark:text-gray-100">{feature}</span>
                              </li>
                            ))}
                            
                            {/* View Details Button */}
                            <div className="mt-6">
                              <Button
                                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center gap-2"
                                onClick={() => handleViewDetails(transformer.title)}
                              >
                                View Details
                                <ArrowRight className="h-5 w-5" />
                              </Button>
                            </div>
                          </ul>
                        </div>
                      </div>
                        {/* Performance metrics section - enhanced modern design */}                      
                      <div className="mt-8">
                        <div className="border-t border-gray-100 dark:border-gray-700 pt-6">                          <h5 className="text-sm font-semibold tracking-wide text-blue-600 dark:text-blue-400 mb-5 flex items-center">
                            <motion.div 
                              className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-md mr-2"
                              animate={{
                                scale: [1, 1.2, 1],
                                rotate: [0, 5, 0, -5, 0],
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "reverse"
                              }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                              </svg>
                            </motion.div>
                            <motion.span
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              className="bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent font-bold"
                              transition={{ duration: 1 }}
                            >
                              PERFORMANCE METRICS
                            </motion.span>
                          </h5>
                          
                          <div className="grid grid-cols-3 gap-2 sm:gap-4 md:gap-6">                            <motion.div 
                              className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-slate-800/80 px-2 sm:px-3 md:px-4 py-3 md:py-4 rounded-xl shadow-lg border border-blue-100/80 dark:border-blue-700/30 hover:shadow-blue-100 dark:hover:shadow-blue-900/20 transition-all duration-300 group"
                              whileHover={{ y: -5, scale: 1.02 }}
                              transition={{ type: "spring", stiffness: 300 }}
                            >
                              <div className="text-gray-900 dark:text-white font-bold text-xl sm:text-2xl md:text-3xl group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors flex items-baseline">
                                <span>98%</span>
                                <motion.span 
                                  className="text-blue-500 font-bold text-sm ml-0.5"
                                  animate={{
                                    y: [0, -3, 0],
                                    scale: [1, 1.2, 1],
                                  }}
                                  transition={{
                                    duration: 1.5,
                                    repeat: Infinity,
                                    repeatType: "reverse"
                                  }}
                                >
                                  +
                                </motion.span>
                              </div>
                              <div className="text-blue-700 dark:text-blue-300 text-sm font-bold mt-1">Efficiency</div>
                            </motion.div>                            <motion.div 
                              className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-slate-800/80 px-2 sm:px-3 md:px-4 py-3 md:py-4 rounded-xl shadow-lg border border-blue-100/80 dark:border-blue-700/30 hover:shadow-blue-100 dark:hover:shadow-blue-900/20 transition-all duration-300 group"
                              whileHover={{ y: -5, scale: 1.02 }}
                              transition={{ type: "spring", stiffness: 300 }}
                            >
                              <div className="text-gray-900 dark:text-white font-bold text-xl sm:text-2xl md:text-3xl group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">&lt;20ms</div>
                              <div className="text-blue-700 dark:text-blue-300 text-sm font-bold mt-1">Response</div>
                            </motion.div>                            <motion.div 
                              className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-slate-800/80 px-2 sm:px-3 md:px-4 py-3 md:py-4 rounded-xl shadow-lg border border-blue-100/80 dark:border-blue-700/30 hover:shadow-blue-100 dark:hover:shadow-blue-900/20 transition-all duration-300 group"
                              whileHover={{ y: -5, scale: 1.02 }}
                              transition={{ type: "spring", stiffness: 300 }}
                            >
                              <div className="text-gray-900 dark:text-white font-bold text-xl sm:text-2xl md:text-3xl group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">±1%</div>
                              <div className="text-blue-700 dark:text-blue-300 text-sm font-bold mt-1">Regulation</div>
                            </motion.div>
                          </div>
                        </div>
                      </div>                    </div>{/* Product image on the right - enhanced with motion and modern style */}                    <div className="md:w-1/2 bg-gradient-to-br from-blue-50 via-blue-50 to-blue-50 dark:from-blue-900/10 dark:via-blue-900 dark:to-blue-900/10 flex items-center justify-center p-6 md:p-4 lg:p-2 rounded-r-2xl relative overflow-hidden">
                      {/* Blue orb decorations */}
                      <motion.div 
                        className="absolute w-40 h-40 rounded-full bg-blue-400/10 blur-3xl"
                        animate={{
                          x: [0, 20, 0],
                          y: [0, -20, 0],
                        }}
                        transition={{
                          duration: 8,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                        style={{ top: '10%', right: '15%' }}
                      />
                      <motion.div 
                        className="absolute w-32 h-32 rounded-full bg-blue-400/10 blur-3xl"
                        animate={{
                          x: [0, -15, 0],
                          y: [0, 15, 0],
                        }}
                        transition={{
                          duration: 7,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                        style={{ bottom: '15%', left: '20%' }}
                      />
                      
                      {/* Animated product image */}                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.7 }}
                        className="relative z-10"
                      >                        <motion.img
                          src={index === 0 ? "/isolation_transformers/X3_-__2-removebg-preview.png" : 
                              index === 1 ? "/isolation_transformers/X2_-_3-removebg-preview.png" : 
                              index === 2 ? "/isolation_transformers/X1_-_1-removebg-preview.png" :
                              "/isolation-banner-1.png"}
                          alt={transformer.title}
                          className="w-auto h-auto max-h-[300px] md:max-h-[500px] lg:max-h-[800px] object-contain drop-shadow-xl transform scale-100 md:scale-125 lg:scale-175" 
                          animate={{
                            y: [0, -15, 0],
                          }}
                          transition={{
                            duration: 6,
                            repeat: Infinity,
                            repeatType: "reverse",
                            ease: "easeInOut"
                          }}
                        />
                          {/* Enhanced highlight glow effect for larger image */}
                        <motion.div 
                          className="absolute inset-0 bg-blue-400/10 blur-2xl rounded-full"
                          animate={{
                            opacity: [0.3, 0.6, 0.3],
                            scale: [0.9, 1.1, 0.9],
                          }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                          style={{ filter: 'blur(25px)' }}
                        />
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Ultra-Modern Technical Specifications Section with 3D Effects */}
      <section id="specifications" className="relative py-16 md:py-24 lg:py-32 overflow-hidden">        {/* Enhanced colorful background with depth and animation - pure blue */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background with vibrant colors */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/80 via-blue-50/90 to-blue-100/80 dark:from-blue-900/40 dark:via-blue-800/40 dark:to-blue-900/40"></div>

          {/* Animated floating elements */}
          <motion.div
            className="absolute top-40 left-[20%] w-96 h-96 rounded-full bg-blue-300/15 blur-3xl"
            animate={{
              y: [0, -50, 0],
              x: [0, 30, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-[15%] w-80 h-80 rounded-full bg-blue-300/15 blur-3xl"
            animate={{
              y: [0, 40, 0],
              x: [0, -20, 0],
              scale: [1.1, 1, 1.1],
              opacity: [0.15, 0.2, 0.15],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />          {/* Circuit board pattern overlay - blue only */}
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-center mb-20 pt-10"
          >            <motion.h2              className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 dark:from-blue-400 dark:via-blue-300 dark:to-blue-400 inline-block mb-6"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Technical Specifications
            </motion.h2>

            <div className="w-32 h-1.5 bg-gradient-to-r from-blue-500 to-blue-600 mx-auto mt-4 rounded-full"></div>            <motion.p              className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-8 text-lg md:text-xl font-medium leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Engineered for <span className="text-blue-600 font-semibold">optimal performance</span> across a wide range of applications with
              <span className="text-blue-600 font-semibold"> industry-leading precision</span>
            </motion.p>
          </motion.div>

          {/* Interactive Specification Cards with 3D Effects */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10 max-w-6xl mx-auto">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.7,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -10,
                  rotateY: 5,
                  rotateX: 5,
                  scale: 1.02,
                  transition: {
                    type: "spring",
                    stiffness: 400,
                    damping: 10
                  }
                }}
                className="group relative"
              >                {/* Card glow effect on hover */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-blue-500/20 to-blue-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                {/* Main card with glass morphism */}                <div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-blue-100/50 dark:border-blue-900/30 relative z-10 h-full transform transition-all duration-500 group-hover:shadow-2xl">
                  {/* Decorative top bar with gradient */}
                  <div className="h-2 w-full bg-gradient-to-r from-blue-500 to-blue-600"></div>

                  <div className="p-4 md:p-8">
                    {/* Category heading with icon */}
                    <div className="flex items-center mb-8">
                      <div className="bg-gradient-to-r from-blue-600 to-blue-600 text-white p-3 rounded-xl mr-4 shadow-lg transform transition-transform duration-500 group-hover:scale-110 group-hover:rotate-3">
                        {index === 0 ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        ) : index === 1 ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        )}
                      </div>
                      <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {spec.category}
                      </h3>
                    </div>

                    {/* Animated specs list */}
                    <div className="space-y-5">
                      {spec.items.map((item, itemIndex) => (
                        <motion.div
                          key={itemIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.5, delay: 0.3 + (itemIndex * 0.1) }}
                          className="relative"
                        >
                          <div className="flex justify-between items-center p-4 rounded-lg bg-gradient-to-r from-blue-50/70 to-blue-50/70 dark:from-blue-900/30 dark:to-blue-900/30 border border-blue-100/50 dark:border-blue-800/30 transform transition-all duration-300 hover:translate-y-[-3px] hover:shadow-md">                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-blue-500 mr-3"></div>
                              <span className="text-gray-800 dark:text-gray-200 font-semibold text-sm md:text-base">{item.label}</span>
                            </div>
                            <motion.div
                              className="font-bold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-3 py-1 rounded-md text-sm md:text-base"
                              whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(59, 130, 246, 0.1)"
                              }}
                            >
                              {item.value}
                            </motion.div>
                          </div>
                        </motion.div>
                      ))}
                    </div>                    {/* Interactive 3D corner accent - blue only */}
                    <motion.div
                      className="absolute -bottom-3 -right-3 w-24 h-24 border-r-2 border-b-2 border-blue-400/50 rounded-br-xl"
                      animate={{
                        opacity: [0.3, 0.5, 0.3],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                      style={{
                        transformStyle: "preserve-3d",
                        transform: "perspective(1000px) rotateX(10deg) rotateY(-10deg)"
                      }}
                    />

                    {/* Animated particle effect */}
                    {Array.from({ length: 3 }).map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-2 h-2 rounded-full bg-blue-400/50"
                        style={{
                          top: `${Math.random() * 100}%`,
                          left: `${Math.random() * 100}%`,
                          zIndex: 20
                        }}
                        animate={{
                          y: [0, Math.random() * 20 - 10],
                          x: [0, Math.random() * 20 - 10],
                          opacity: [0.5, 0.2, 0.5],
                          scale: [1, 1.5, 1],
                        }}
                        transition={{
                          duration: 2 + Math.random() * 2,
                          repeat: Infinity,
                          repeatType: "reverse",
                        }}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Information Section - Light Blue Box Style */}      <section className="py-10 md:py-16 relative overflow-hidden">
        <div className="container mx-auto px-4 max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="rounded-xl bg-gradient-to-br from-blue-100 via-blue-50 to-blue-100 shadow-md p-6 md:p-10 relative overflow-hidden"
          >
            {/* Decorative elements for contact section */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
            <motion.div
              className="absolute top-1/2 right-1/4 w-20 h-20 bg-blue-300/20 rounded-full blur-xl"
              animate={{
                y: [0, -15, 0],
                opacity: [0.2, 0.3, 0.2],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            ></motion.div>
            <div className="text-center">
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-3xl font-bold text-blue-700 mb-4"
              >
                Need a Custom Isolation Transformer?
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-blue-600 max-w-3xl mx-auto mb-8"
              >
                Our engineers can design transformers to your specific requirements with industry-leading performance and reliability.
                Get in touch today to discuss your needs.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Button
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 to-blue-700 text-white px-8 py-3 rounded-md flex items-center gap-2 mx-auto shadow-lg hover:shadow-blue-300/30 transition-all duration-300"
                  onClick={() => window.location.href = "/contact/sales"}
                >
                  <Mail className="h-5 w-5" />
                  <span className="font-semibold">Contact Our Experts</span>
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </PageLayout>
  );
};

export default IsolationTransformers;