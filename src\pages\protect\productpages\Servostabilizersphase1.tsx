import React from 'react';
import { Shield, Zap, Settings, Headphones, CheckCircle, Monitor, BarChart3, Lock, Star, Mail, Download, Info } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const ServoStabilizersPhase1 = () => {

  const specifications = [
    { parameter: "Rating (kVA)", "1kVA": "1 kVA", "2-3kVA": "2-3 kVA", "4-5kVA": "4-5 kVA", "7.5-10kVA": "7.5-10 kVA", "15-20kVA": "15-20 kVA" },
    { parameter: "Input Voltage Range", "1kVA": "190-250V", "2-3kVA": "190-250V", "4-5kVA": "190-250V", "7.5-10kVA": "190-250V", "15-20kVA": "190-250V" },
    { parameter: "Output Voltage", "1kVA": "220V ±1%", "2-3kVA": "220V ±1%", "4-5kVA": "220V ±1%", "7.5-10kVA": "220V ±1%", "15-20kVA": "220V ±1%" },
    { parameter: "Input Frequency", "1kVA": "47-53 Hz", "2-3kVA": "47-53 Hz", "4-5kVA": "47-53 Hz", "7.5-10kVA": "47-53 Hz", "15-20kVA": "47-53 Hz" },
    { parameter: "Digital Display", "1kVA": "3 digit, 7 segment", "2-3kVA": "3 digit, 7 segment", "4-5kVA": "3 digit, 7 segment", "7.5-10kVA": "3 digit, 7 segment", "15-20kVA": "3 digit, 7 segment" },
    { parameter: "Voltage Accuracy", "1kVA": "1% ±2 digit", "2-3kVA": "1% ±2 digit", "4-5kVA": "1% ±2 digit", "7.5-10kVA": "1% ±2 digit", "15-20kVA": "1% ±2 digit" },
    { parameter: "Under/Over Voltage Trip", "1kVA": "-5% / +10%", "2-3kVA": "-5% / +10%", "4-5kVA": "-5% / +10%", "7.5-10kVA": "-5% / +10%", "15-20kVA": "-5% / +10%" },
    { parameter: "Response Time", "1kVA": "< 20 ms", "2-3kVA": "< 20 ms", "4-5kVA": "< 20 ms", "7.5-10kVA": "< 20 ms", "15-20kVA": "< 20 ms" },
    { parameter: "Efficiency", "1kVA": "> 98%", "2-3kVA": "> 98%", "4-5kVA": "> 98%", "7.5-10kVA": "> 98%", "15-20kVA": "> 98%" },
    { parameter: "Transient Protection", "1kVA": "MOV Spike Suppressor", "2-3kVA": "MOV Spike Suppressor", "4-5kVA": "MOV Spike Suppressor", "7.5-10kVA": "MOV Spike Suppressor", "15-20kVA": "MOV Spike Suppressor" },
    { parameter: "Input Termination", "1kVA": "1.5m cable with 5A plug", "2-3kVA": "1.5m cable with 5A plug", "4-5kVA": "1.5m cable with 5A plug", "7.5-10kVA": "6A terminal block", "15-20kVA": "100A terminal block" },
    { parameter: "Output Termination", "1kVA": "1x5/15A socket", "2-3kVA": "1x5/15A socket", "4-5kVA": "2x5/15A socket", "7.5-10kVA": "Terminal block", "15-20kVA": "Terminal block" },
    { parameter: "Protection Features", "1kVA": "Complete Protection", "2-3kVA": "Complete Protection", "4-5kVA": "Complete Protection", "7.5-10kVA": "Complete Protection", "15-20kVA": "Complete Protection" },
    { parameter: "Manual Bypass", "1kVA": "Provided", "2-3kVA": "Provided", "4-5kVA": "Provided", "7.5-10kVA": "Provided", "15-20kVA": "Provided" },
    { parameter: "Operating Temperature", "1kVA": "0-50°C", "2-3kVA": "0-50°C", "4-5kVA": "0-50°C", "7.5-10kVA": "0-50°C", "15-20kVA": "0-50°C" },
    { parameter: "Warranty", "1kVA": "1 Year Comprehensive", "2-3kVA": "1 Year Comprehensive", "4-5kVA": "1 Year Comprehensive", "7.5-10kVA": "1 Year Comprehensive", "15-20kVA": "1 Year Comprehensive" }
  ];

  const features = [
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "True RMS Correction",
      items: [
        "Microprocessor based control system for measurement and correction",
        "Digital Voltage, Current display through high visibility LED"
      ]
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Reliable by Design",
      items: [
        "Stabilizers designed to its rated capacity",
        "Lowest failure rates and life cycle cost in the industry",
        "1 year 'No Questions Asked' guarantee"
      ]
    },
    {
      icon: <Lock className="w-8 h-8" />,
      title: "Complete Protection",
      items: [
        "Short Circuit",
        "Under voltage",
        "Over voltage",
        "Electronic CT based Overload trip",
        "Built-in Spike Suppressor"
      ]
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "Dependable After Sales Support",
      items: [
        "Wide service network covering 100 + locations across India",
        "Committed Service response",
        "within 6 hours in service towns and within 24 hours in the same State"
      ]
    }
  ];

  const productRanges = [
    {
      title: "Regular Range: 190 - 250V",
      description: "for factories, offices & urban locations"
    },
    {
      title: "Wide Range: 170 - 270V",
      description: "for semi-urban locations with wide fluctuations"
    },
    {
      title: "Custom Range",
      description: "Can be provided for specific needs"
    }
  ];

  return (
    <PageLayout
      title="Servo Stabilizers - 1 Phase"
      subtitle="Advanced voltage protection solutions for your needs"
      category="protect"
    >
      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16">
        {/* Hero Content Section - Integrated with PageLayout */}
        <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white relative">
          {/* Background Image with Transparency */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
            style={{
              backgroundImage: "url('https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
            }}
          ></div>
          <div className="w-full px-6 md:px-12 lg:px-16 py-16 md:py-24 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-8">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Servo Controlled Voltage Stabilizers
                  <span className="block text-blue-300 text-3xl md:text-4xl mt-2">1 Phase</span>
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                  Compact and reliable voltage protection solution designed to safeguard your valuable appliances from voltage fluctuations with True RMS correction and complete protection features.
                </p>
                <div className="flex items-center justify-center gap-6">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-7 h-7 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-4 text-xl text-blue-100 font-medium">Guardian of Your Appliances</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-6 justify-center pt-4">
                  <button
                    onClick={() => {
                      // Redirect to Sales page for quote
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Mail className="w-6 h-6 inline mr-3" />
                    Get Quote
                  </button>
                  <button
                    onClick={() => {
                      // Create a link to download the PDF brochure
                      const link = document.createElement('a');
                      link.href = '/brochures/servo-stabilizers-1phase-brochure.pdf';
                      link.download = 'Servo-Stabilizers-1Phase-Brochure.pdf';
                      link.click();
                    }}
                    className="bg-white text-blue-800 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Download className="w-6 h-6 inline mr-3" />
                    Download Brochure
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Servo Voltage Protection
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Servo Controlled Voltage Stabilizers provide superior voltage regulation with True RMS correction, ensuring optimal protection for your valuable appliances and equipment. Built with microprocessor-based control systems for precise measurement and correction.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Product Range Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-100 to-blue-200">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Product Range for Your Needs
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Choose from our comprehensive range of voltage stabilizers designed to meet different operating environments and voltage fluctuation patterns.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {productRanges.map((range, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-2xl p-10 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-2 border-blue-300">
                <div className="flex items-center mb-8">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-5 shadow-xl">
                    <Settings className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 ml-6">{range.title}</h3>
                </div>
                <p className="text-lg text-gray-800 leading-relaxed text-justify font-medium">{range.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all rating ranges.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      1 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      2-3 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      4-5 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      7.5-10 kVA
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      15-20 kVA
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['1kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['2-3kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['4-5kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['7.5-10kVA']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['15-20kVA']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per IS 9815 standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Protect Your Valuable Appliances Today
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Choose from our comprehensive range of servo controlled voltage stabilizers designed to meet your specific power protection needs. Get expert consultation and support.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
};

export default ServoStabilizersPhase1;