import React, { useState, useEffect, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  CheckCircle2, ArrowRight, Zap, Cloud,
  BarChart2, Activity, Droplet, Gauge,
  Shield, Send, FileText, Download
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";

// CountUp Animation Component
const CountUp = ({
  to,
  from = 0,
  duration = 2,
  delay = 0,
  className = "",
}) => {
  const ref = useRef(null);
  const [isInView, setIsInView] = useState(false);
  const [count, setCount] = useState(from);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.disconnect();
      }
    };
  }, []);

  useEffect(() => {
    let frame: number;
    let startTime: number;

    if (isInView) {
      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);

        setCount(Math.floor(from + (to - from) * progress));

        if (progress < 1) {
          frame = requestAnimationFrame(animate);
        }
      };

      setTimeout(() => {
        frame = requestAnimationFrame(animate);
      }, delay * 1000);
    }

    return () => {
      if (frame) {
        cancelAnimationFrame(frame);
      }
    };
  }, [isInView, from, to, duration, delay]);

  return <span ref={ref} className={className}>{count}</span>;
};

// Feature Card Component
const FeatureCard = ({
  icon,
  title,
  description,
  index = 0,
  color = "green",
  iconColor = "green-500",
  delay = 0
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.5, delay: delay + index * 0.1 }}
    className={`p-6 rounded-2xl bg-gradient-to-br from-white to-${color}-50 dark:from-slate-800 dark:to-${color}-900/30 shadow-xl border border-${color}-100 dark:border-${color}-800/50 group h-full`}
    whileHover={{
      y: -8,
      transition: { duration: 0.2 },
      boxShadow: `0 20px 40px rgba(16, 185, 129, 0.2), 0 10px 20px rgba(16, 185, 129, 0.1)`
    }}
  >
    <div className="flex items-center mb-4">
      <div className="relative flex-shrink-0 mr-4">
        <div className={`absolute inset-0 bg-${iconColor}/20 rounded-full blur-xl transform scale-150`}></div>
        <div className={`text-${iconColor} relative z-10`}>
          {React.isValidElement(icon) ? icon : <span className="text-3xl">{icon}</span>}
        </div>
      </div>
      <h4 className="font-bold text-slate-800 dark:text-slate-200 text-xl">{title}</h4>
    </div>
    <p className="text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-colors duration-300">
      {description}
    </p>
  </motion.div>
);

// StatsCard Component
const StatsCard = ({ number, suffix = "", text, icon, color1, color2, description, index = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    className="relative group"
  >
    <div className="absolute -inset-3 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

    <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl relative overflow-hidden backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 h-full flex flex-col justify-between transform group-hover:translate-y-[-8px] transition-transform duration-500"
      style={{
        boxShadow: "0 20px 40px rgba(16, 185, 129, 0.08), 0 10px 20px rgba(16, 185, 129, 0.06)"
      }}
    >
      {/* Colored gradient blob in corner */}
      <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full"
        style={{
          background: `radial-gradient(circle, ${color1}20, transparent 70%)`,
          filter: "blur(25px)"
        }}>
      </div>

      <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
        style={{
          background: `radial-gradient(circle, ${color2}20, transparent 70%)`,
          filter: "blur(25px)"
        }}>
      </div>

      <div className="text-4xl mb-6"
        style={{
          color: color1
        }}>
        {icon}
      </div>

      <div>
        <div className="text-5xl font-bold mb-2 bg-clip-text text-transparent relative z-10"
          style={{
            backgroundImage: `linear-gradient(135deg, ${color1}, ${color2})`
          }}>
          <CountUp
            to={number}
            from={0}
            duration={2.5}
            delay={0.5 + index * 0.2}
            className="inline-block"
          />
          <span>{suffix}</span>
        </div>
        <div className="text-xl font-semibold text-slate-700 dark:text-slate-200 mb-3">{text}</div>
        <div className="text-sm text-slate-500 dark:text-slate-400">{description}</div>
      </div>
    </div>
  </motion.div>
);

// PDF Viewer Component
const PdfViewer = ({ showPdfViewer, setShowPdfViewer, pdfUrl, title }) => {
  const handleDownloadPdf = () => {
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = pdfUrl;
    a.setAttribute('download', 'ALENCLOUD-EMS-Brochure.pdf');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident/') !== -1) {
      window.open(pdfUrl, '_blank');
    }
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${showPdfViewer ? '' : 'hidden'}`}>
      <div className="absolute inset-0 bg-black bg-opacity-70" onClick={() => setShowPdfViewer(false)}></div>
      <div className="relative bg-white rounded-xl p-6 w-full max-w-5xl max-h-[90vh] overflow-hidden">
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          onClick={() => setShowPdfViewer(false)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="flex items-center justify-between mb-4 pb-4 border-b">
          <h3 className="text-xl font-bold text-green-800">{title || "ALENCLOUD Energy Management Systems Brochure"}</h3>
          <button
            onClick={handleDownloadPdf}
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Download PDF
          </button>
        </div>

        <div className="w-full h-[70vh]">
          <object
            data={pdfUrl}
            type="application/pdf"
            className="w-full h-full"
          >
            <div className="flex flex-col items-center justify-center h-full bg-gray-100 rounded-lg p-8 text-center">
              <p className="text-gray-600 mb-4">
                PDF preview is not available in your browser.
              </p>
              <button
                onClick={handleDownloadPdf}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </button>
            </div>
          </object>
        </div>
      </div>
    </div>
  );
};

const CloudEnergyManagement = () => {
  const [showPdfViewer, setShowPdfViewer] = useState<boolean>(false);

  const { scrollYProgress } = useScroll();
  const heroOpacity = useTransform(scrollYProgress, [0, 0.2], [1, 0.7]);
  const heroScale = useTransform(scrollYProgress, [0, 0.2], [1, 0.95]);
  const heroY = useTransform(scrollYProgress, [0, 0.2], [0, 50]);

  const statsData = [
    {
      number: 15,
      suffix: "+",
      text: "Years Experience",
      icon: "📅",
      color1: "#10B981",
      color2: "#34D399",
      description: "Cloud energy expertise"
    },
    {
      number: 500,
      suffix: "+",
      text: "Remote Sites",
      icon: "🌐",
      color1: "#34D399",
      color2: "#6EE7B7",
      description: "Monitored worldwide"
    },
    {
      number: 20000,
      suffix: "+",
      text: "Monitoring Points",
      icon: "📊",
      color1: "#6EE7B7",
      color2: "#A7F3D0",
      description: "Real-time data collection"
    },
    {
      number: 8,
      suffix: "+",
      text: "$ Million Saved",
      icon: "💰",
      color1: "#A7F3D0",
      color2: "#D1FAE5",
      description: "For our clients annually"
    }
  ];

  const keyBenefits = [
    {
      text: "Simple to install and connect",
      icon: <Zap className="h-6 w-6" />,
      description: "Plug-and-play installation with minimal IT infrastructure requirements"
    },
    {
      text: "Monitor hundreds of remote sites in a single portal",
      icon: <BarChart2 className="h-6 w-6" />,
      description: "Unified dashboard for all your facilities regardless of location"
    },
    {
      text: "Measure usage of Electricity, Water, Air or any resource",
      icon: <Gauge className="h-6 w-6" />,
      description: "Comprehensive monitoring of all resource consumption in real-time"
    },
    {
      text: "EMS in your pocket – Apps for iOS and Android",
      icon: <Cloud className="h-6 w-6" />,
      description: "Mobile-first design with full functionality on smartphone apps"
    },
    {
      text: "Alerts and Notifications for any critical deviations",
      icon: <Activity className="h-6 w-6" />,
      description: "Instant alerts via SMS, email, or push notifications for immediate action"
    },
    {
      text: "Wide variety of trends and reports for analysis",
      icon: <Droplet className="h-6 w-6" />,
      description: "Advanced analytics with customizable reporting options"
    }
  ];

  const reportFeatures = [
    {
      text: "Real-time trends for any parameter",
      icon: <Activity className="h-5 w-5 text-green-500" />,
      description: "Monitor live data as it happens with millisecond precision"
    },
    {
      text: "Historical trends for any parameter and period",
      icon: <BarChart2 className="h-5 w-5 text-green-500" />,
      description: "Analyze past performance with flexible date ranges"
    },
    {
      text: "Energy Reports – Shift, Day, Month – in PDF and Excel",
      icon: <Cloud className="h-5 w-5 text-green-500" />,
      description: "Export professional reports in multiple formats"
    },
    {
      text: "Energy Target vs Actual Deviation Report",
      icon: <Gauge className="h-5 w-5 text-green-500" />,
      description: "Track performance against set goals and benchmarks"
    },
    {
      text: "Max. Demand Crossing View for Incomer",
      icon: <Zap className="h-5 w-5 text-green-500" />,
      description: "Identify peak consumption periods for demand management"
    },
    {
      text: "Harmonics Trends and Analysis",
      icon: <Activity className="h-5 w-5 text-green-500" />,
      description: "Monitor power quality for sensitive equipment protection"
    },
    {
      text: "Time Of Day Energy Reports",
      icon: <Cloud className="h-5 w-5 text-green-500" />,
      description: "Optimize usage based on time-of-use pricing models"
    },
    {
      text: "Customizable Alarm Configuration",
      icon: <Shield className="h-5 w-5 text-green-500" />,
      description: "Set personalized thresholds for alert notifications"
    },
    {
      text: "Auto Email of Daily Energy reports",
      icon: <Send className="h-5 w-5 text-green-500" />,
      description: "Scheduled delivery of reports to stakeholders"
    }
  ];

  const handleEnquire = () => {
    window.location.href = "/contact/sales";
  };

  const handleBrochure = () => {
    setShowPdfViewer(true);
  };

  return (
    <PageLayout
      title="Cloud Energy Management"
      subtitle="Cloud-based solutions for remote energy monitoring and optimization"
      category="conserve"
    >
      {/* Hero Section - Enhanced with Parallax and Dynamic Background */}
      <section className="py-20 mb-0 relative overflow-hidden">
        {/* Dynamic background with moving gradients */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/30 dark:via-emerald-900/30 dark:to-teal-900/30"></div>
          <motion.div
            className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.3),transparent_70%)]"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.2, 0.3, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          ></motion.div>
        </div>

        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">
            <motion.div
              className="lg:col-span-6"
              style={{
                opacity: heroOpacity,
                y: heroY,
                scale: heroScale
              }}
            >
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, ease: "easeOut" }}
                className="relative"
              >
                {/* Decorative element */}
                <div className="absolute -left-6 -top-6 w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-emerald-300 opacity-20 blur-2xl"></div>

                <h1 className="text-5xl font-bold mb-6 leading-tight bg-clip-text text-transparent bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 dark:from-green-400 dark:via-emerald-400 dark:to-teal-400">
                  Access your EMS from <span className="relative inline-block">
                    anywhere, anytime
                    <svg className="absolute -bottom-2 left-0 w-full h-2 text-emerald-400/30" viewBox="0 0 100 12" preserveAspectRatio="none">
                      <path d="M0,0 Q50,12 100,0" stroke="currentColor" strokeWidth="8" fill="none" />
                    </svg>
                  </span>
                </h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
                  className="text-slate-700 dark:text-slate-200 text-xl mb-8 leading-relaxed font-medium"
                >
                  ALENCLOUD provides a complete cloud-based Energy Management System that lets you
                  <span className="text-emerald-600 dark:text-emerald-400 font-semibold"> monitor, analyze, and optimize</span> energy usage across all your facilities from anywhere in the world.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
                  className="flex flex-wrap gap-5"
                >
                  <Button
                    className="bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 text-white shadow-lg font-semibold text-base px-8 py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1"
                    style={{ boxShadow: "0 10px 30px -5px rgba(16, 185, 129, 0.5)" }}
                    onClick={handleEnquire}
                  >
                    <span className="mr-2">ENQUIRE NOW</span>
                    <ArrowRight className="h-5 w-5" />
                  </Button>

                  <Button
                    className="bg-white border-2 border-green-500 text-green-600 hover:bg-green-50 shadow-md font-semibold text-base px-8 py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center"
                    onClick={handleBrochure}
                  >
                    <span className="mr-2">VIEW BROCHURE</span>
                    <FileText className="h-5 w-5" />
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-6 relative"
            >
              <div className="absolute -inset-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-3xl blur-xl -z-10 opacity-70"></div>
              <div className="rounded-3xl overflow-hidden shadow-2xl transform hover:scale-[1.02] transition-all duration-700">
                <div className="relative">
                  {/* Glassmorphism overlay at the top of the image */}
                  <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-b from-white/30 to-transparent backdrop-blur-sm z-10 flex items-center px-6">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="ml-4 text-xs font-semibold text-white/90">ALENCLOUD Dashboard</div>
                  </div>

                  <img
                    src="/Alencloud.png"
                    alt="ALENCLOUD Dashboard"
                    className="w-full h-auto object-cover"
                  />

                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section - With Interactive Cards */}
      <section className="py-24 relative overflow-hidden">
        {/* Gradient background with animated overlay */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"></div>
          <div className="absolute inset-0 opacity-30 bg-[linear-gradient(60deg,transparent_0%,rgba(16,185,129,0.1)_20%,transparent_30%)]" style={{ backgroundSize: "200% 200%", animation: "gradient-shift 15s ease infinite" }}></div>
        </div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block">
              Our Impact By Numbers
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                number={stat.number}
                suffix={stat.suffix}
                text={stat.text}
                icon={stat.icon}
                color1={stat.color1}
                color2={stat.color2}
                description={stat.description}
                index={index}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Key Benefits Section - Redesigned with Better Visual Hierarchy */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-green-900/30 -z-10"></div>

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-5 -z-10"
          style={{
            backgroundImage: "radial-gradient(circle at 20px 20px, rgba(16, 185, 129, 0.2) 2px, transparent 0)",
            backgroundSize: "20px 20px"
          }}
        ></div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <motion.h2
              className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block"
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              KEY BENEFITS
            </motion.h2>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {keyBenefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-100 dark:border-slate-700 p-6 h-full relative overflow-hidden group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{
                  y: -10,
                  transition: { type: "spring", stiffness: 300, damping: 25 }
                }}
              >
                {/* Background gradient blob that moves on hover */}
                <div className="absolute -right-20 -bottom-20 w-40 h-40 bg-green-500/5 rounded-full group-hover:bg-green-500/10 transition-colors duration-300"></div>
                <div className="absolute -left-20 -top-20 w-40 h-40 bg-emerald-500/5 rounded-full group-hover:bg-emerald-500/10 transition-colors duration-300"></div>

                {/* Icon with animated background */}
                <div className="relative mb-4 w-14 h-14 flex items-center justify-center">
                  <div className="absolute inset-0 bg-green-100 dark:bg-green-900/30 rounded-lg transform rotate-3 group-hover:rotate-6 transition-transform duration-300"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg transform -rotate-3 group-hover:rotate-0 transition-transform duration-300"></div>
                  <div className="relative text-white z-10">
                    {benefit.icon}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100 mb-3 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                  {benefit.text}
                </h3>

                <p className="text-slate-600 dark:text-slate-300 text-sm group-hover:text-slate-700 dark:group-hover:text-slate-200 transition-colors duration-300">
                  {benefit.description}
                </p>

                {/* Animated arrow indicator */}
                <div className="mt-4 h-0 overflow-hidden group-hover:h-6 transition-all duration-300 opacity-0 group-hover:opacity-100">
                    <div className="flex items-center text-green-500 font-medium text-sm">
                      <span>Learn more</span>
                      <motion.div
                        animate={{ x: [0, 5, 0] }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="ml-2"
                      >
                        <ArrowRight className="h-4 w-4" />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features & Reports Section - Improved Layout */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-900/20 dark:to-emerald-900/20 -z-10"></div>

        {/* Subtle animated pattern */}
        <motion.div
          className="absolute inset-0 opacity-5 -z-10"
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%'],
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 30,
            ease: "linear"
          }}
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2310b981" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'
          }}
        />

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block">
              FEATURES & REPORTS
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
            <motion.p
              className="text-center text-gray-700 dark:text-gray-300 max-w-2xl mx-auto mt-6 text-lg"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Comprehensive tools and analytics to help you maximize energy efficiency
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white dark:bg-slate-800 rounded-3xl p-10 shadow-xl border border-slate-100 dark:border-slate-700 relative mb-16"
          >
            {/* Decorative elements */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-green-400/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-emerald-400/10 rounded-full blur-3xl"></div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reportFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  className="p-5 rounded-xl border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/80 hover:shadow-md transition-all duration-300 group relative overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 + (index * 0.05) }}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  {/* Diagonal decorative gradient line */}
                  <div className="absolute h-px w-40 bg-gradient-to-r from-green-200 to-emerald-200 dark:from-green-600/30 dark:to-emerald-600/30 top-0 -right-10 rotate-45 transform origin-left"></div>

                  <div className="flex items-start">
                    <div className="mr-4 flex-shrink-0 w-10 h-10 rounded-lg bg-gradient-to-br from-green-100 to-green-50 dark:from-green-900/50 dark:to-green-800/30 flex items-center justify-center">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-slate-800 dark:text-slate-200 mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                        {feature.text}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* System Architecture Section - Simplified with flattened structure */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/80 to-green-50/80 dark:from-emerald-900/20 dark:to-green-900/20 -z-10"></div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block">
              ALENCLOUD EMS
            </h2>
            <span className="block text-lg font-medium mt-3 text-slate-600 dark:text-slate-300">
              Advanced Cloud Architecture
            </span>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          {/* Simplified architecture image without nested containers */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex justify-center mb-14"
          >
            <img
              src="/background_images/Schematic-EMS-on-Cloud.jpg"
              alt="EMS System Architecture"
              className="max-w-full rounded-2xl shadow-lg border border-slate-100 dark:border-slate-700"
              style={{ maxHeight: "1200px" }}
            />
          </motion.div>

          {/* Features grid directly below image */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <motion.div
              className="border border-green-100 dark:border-green-800/30 rounded-lg p-6 bg-green-50/50 dark:bg-green-900/20 backdrop-blur-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="font-semibold mb-4 text-green-700 dark:text-green-300">Cloud Server Features</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Secure access with 256-bit SSL encryption</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Automatic data backups and disaster recovery</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Global CDN for fast access from anywhere</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">99.9% uptime SLA guarantee</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              className="border border-green-100 dark:border-green-800/30 rounded-lg p-6 bg-green-50/50 dark:bg-green-900/20 backdrop-blur-sm hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="font-semibold mb-4 text-green-700 dark:text-green-300">Remote Site Integration</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Compatible with all major metering and monitoring systems</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Simple setup with automatic data syncing</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Cellular 4G/5G fallback when internet is unavailable</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 flex-shrink-0">•</span>
                  <span className="text-sm text-slate-600 dark:text-slate-300">Local data storage during connectivity loss</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Need More Information Section */}
      <section className="py-10 md:py-16 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto bg-green-50 dark:bg-green-900/30 rounded-2xl shadow-lg overflow-hidden">
            <div className="py-12 px-8 md:px-12 text-center">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-4xl font-bold text-slate-800 dark:text-slate-100 mb-6"
              >
                Need More Information?
              </motion.h2>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto mb-8 text-lg"
              >
                Our team of experts is ready to help you with product specifications, custom solutions,
                pricing, and any other details you need about <span className="text-green-600 dark:text-green-400 font-semibold">ALENSOFT Energy Management Systems</span>.
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Button
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-md flex items-center gap-2 mx-auto shadow-lg hover:shadow-green-300/30 transition-all duration-300"
                  onClick={handleEnquire}
                >
                  <span className="font-semibold">Contact Our Experts</span>
                  <ArrowRight className="h-5 w-5" />
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* PDF Viewer Component */}
      <PdfViewer
        showPdfViewer={showPdfViewer}
        setShowPdfViewer={setShowPdfViewer}
        pdfUrl="/KRYKARD-Comprehensive-Product-Catalogue.pdf"
        title="KRYKARD Product Catalogue"
      />
    </PageLayout>
  );
};

export default CloudEnergyManagement;