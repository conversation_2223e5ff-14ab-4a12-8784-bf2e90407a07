import React, { useEffect, useRef } from "react";
import { motion, useInView, useMotionValue, useSpring, useScroll, useTransform } from "framer-motion";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";

// PDF URL for brochure
const PDF_URL = "/KRYKARD-Comprehensive-Product-Catalogue.pdf";

// CountUp Component (Improved with smooth transitions)
const CountUp = ({
    to,
    from = 0,
    direction = "up",
    delay = 0,
    duration = 2,
    className = "",
    startWhen = true,
    separator = "",
    onStart,
    onEnd,
}) => {
    const ref = useRef(null);
    const motionValue = useMotionValue(direction === "down" ? to : from);
    // Enhanced parameters for smoother animation
    const damping = 30 + 40 * (1 / duration);
    const stiffness = 120 * (1 / duration);
    const springValue = useSpring(motionValue, {
        damping,
        stiffness,
    });
    const isInView = useInView(ref, { once: true, margin: "-10% 0px" });

    // Set initial text content
    useEffect(() => {
        if (ref.current) {
            ref.current.textContent = String(direction === "down" ? to : from);
        }
    }, [from, to, direction]);

    // Start animation when in view
    useEffect(() => {
        if (isInView && startWhen) {
            if (typeof onStart === "function") {
                onStart();
            }
            const timeoutId = setTimeout(() => {
                motionValue.set(direction === "down" ? from : to);
            }, delay * 1000);
            const durationTimeoutId = setTimeout(() => {
                if (typeof onEnd === "function") {
                    onEnd();
                }
            }, delay * 1000 + duration * 1000);
            return () => {
                clearTimeout(timeoutId);
                clearTimeout(durationTimeoutId);
            };
        }
    }, [isInView, startWhen, motionValue, direction, from, to, delay, onStart, onEnd, duration]);

    // Update text with formatted number
    useEffect(() => {
        const unsubscribe = springValue.on("change", (latest) => {
            if (ref.current) {
                const options = {
                    useGrouping: !!separator,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                };
                const formattedNumber = Intl.NumberFormat("en-US", options).format(
                    Number(latest.toFixed(0))
                );
                ref.current.textContent = separator
                    ? formattedNumber.replace(/,/g, separator)
                    : formattedNumber;
            }
        });
        return () => unsubscribe();
    }, [springValue, separator]);

    return <span className={`${className} inline-block`} ref={ref} />;
};

// Animated Badge Component for Reusability
const AnimatedBadge = ({ icon, text, color, delay = 0 }) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay }}
        className={`flex items-center px-5 py-3 rounded-full shadow-lg border ${color} backdrop-blur-sm`}
        whileHover={{ scale: 1.05, y: -3, transition: { duration: 0.2 } }}
    >
        <span className="text-2xl mr-2">{icon}</span>
        <span className="font-medium">{text}</span>
    </motion.div>
);

// Feature Card Component for Reusability
const FeatureCard = ({ icon, title, description, color, delay = 0, index = 0 }) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: delay + index * 0.1 }}
        className={`p-6 rounded-2xl bg-gradient-to-br from-white to-${color}-50 dark:from-slate-800 dark:to-${color}-900/30 shadow-xl border border-${color}-100 dark:border-${color}-800/50 group`}
        whileHover={{
            y: -8,
            transition: { duration: 0.2 },
            boxShadow: `0 20px 40px rgba(16, 185, 129, 0.2), 0 10px 20px rgba(16, 185, 129, 0.1)`
        }}
    >
        <div className="flex items-center mb-4">
            <div className="relative flex-shrink-0 mr-4">
                <div className={`absolute inset-0 bg-${color}-500/20 rounded-full blur-xl transform scale-150`}></div>
                <div className={`text-4xl text-${color}-500 dark:text-${color}-400 relative z-10`}>{icon}</div>
            </div>
            <h4 className="font-bold text-slate-800 dark:text-slate-200 text-xl">{title}</h4>
        </div>
        <p className="text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-colors duration-300">
            {description}
        </p>
    </motion.div>
);

const OnPremiseSystems = () => {
  // Enhanced stats data with more dynamic colors and detailed information
  const statsData = [
    {
      number: 20,
      suffix: "+",
      text: "Years Experience",
      icon: "📅",
      color1: "#10B981",
      color2: "#34D399",
      description: "Decades of industry expertise"
    },
    {
      number: 200,
      suffix: "+",
      text: "EMS Networks",
      icon: "🌐",
      color1: "#34D399",
      color2: "#6EE7B7",
      description: "Deployed globally"
    },
    {
      number: 12000,
      suffix: "+",
      text: "Monitoring Points",
      icon: "📊",
      color1: "#6EE7B7",
      color2: "#A7F3D0",
      description: "Real-time data collection"
    },
    {
      number: 10,
      suffix: "+",
      text: "$ Million Saved",
      icon: "💰",
      color1: "#A7F3D0",
      color2: "#D1FAE5",
      description: "For our clients annually"
    }
  ];

  // Enhanced management steps with more detailed content
  const managementSteps = [
    {
      number: 1,
      title: "GET your data",
      description: "Collect comprehensive data from all your energy & resource consuming points with our advanced IoT sensors and smart meters",
      color: "#059669", // Emerald-600
      icon: "📥"
    },
    {
      number: 2,
      title: "SET your targets",
      description: "Define customized targets for each consumption point and track progress in real-time against industry benchmarks and facility norms",
      color: "#10B981", // Emerald-500
      icon: "🎯"
    },
    {
      number: 3,
      title: "SAVE your energy",
      description: "Analyze deviation patterns from targets to identify cost optimization opportunities and implement pollution control measures",
      color: "#34D399", // Emerald-400
      icon: "⚡"
    },
    {
      number: 4,
      title: "REVIEW your results",
      description: "Generate comprehensive reports to track achievement of financial and environmental goals with actionable insights",
      color: "#6EE7B7", // Emerald-300
      icon: "📈"
    }
  ];

  // Enhanced features with more descriptive content
  const features = [
    {
      title: "Universal Meter Integration",
      icon: "🔄",
      description: "Connect with any meter brand or protocol"
    },
    {
      title: "Cloud & On-Premise Compatible",
      icon: "☁️",
      description: "Flexible deployment options"
    },
    {
      title: "Cross-Platform Support",
      icon: "💻",
      description: "Works on Windows, Mac, Linux & mobile"
    },
    {
      title: "Cloud-Ready Architecture",
      icon: "🚀",
      description: "Easily scale with your business needs"
    },
    {
      title: "Multi-system Connectivity",
      icon: "🔌",
      description: "Seamless integration with existing systems"
    },
    {
      title: "High-Speed Monitoring",
      icon: "⚡",
      description: "Sub-second data resolution"
    },
    {
      title: "Energy Accounting",
      icon: "💲",
      description: "Detailed cost allocation & budgeting"
    },
    {
      title: "Predictive Analytics",
      icon: "🔮",
      description: "AI-powered consumption forecasting"
    }
  ];

  // Enhanced reports data
  const reports = [
    {
      title: "Energy Consumption Analytics",
      description: "Detailed breakdown of energy usage patterns with time-series analysis and peak demand identification",
      icon: "📊",
      color: "emerald"
    },
    {
      title: "Financial Impact Analysis",
      description: "Comprehensive cost analysis with ROI calculations and investment recommendations for energy efficiency",
      icon: "💰",
      color: "green"
    },
    {
      title: "Sustainability Metrics",
      description: "Environmental impact reports with carbon footprint reduction tracking and ESG compliance monitoring",
      icon: "🌱",
      color: "teal"
    }
  ];

  // For enhanced parallax scrolling effects
  const { scrollYProgress } = useScroll();
  const heroOpacity = useTransform(scrollYProgress, [0, 0.2], [1, 0.7]);
  const heroScale = useTransform(scrollYProgress, [0, 0.2], [1, 0.95]);
  const heroY = useTransform(scrollYProgress, [0, 0.2], [0, 50]);

  // No PDF viewer state needed

  // Function handlers for buttons
  const handleEnquiryClick = () => {
    try {
      window.location.href = "/contact/sales";
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  const handleBrochureClick = () => {
    try {
      window.open(PDF_URL, '_blank');
    } catch (error) {
      console.error("Error opening brochure:", error);
    }
  };

  const handleContactSalesClick = () => {
    try {
      window.location.href = "/contact/sales";
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  return (
    <PageLayout
      title="On-Premise Energy Management"
      subtitle="Advanced local systems for direct control and real-time monitoring"
      category="conserve"
    >
      {/* Hero Section - Enhanced with Parallax and Glassmorphism */}
      <section className="py-20 mb-0 relative overflow-hidden">
        {/* Dynamic background with moving gradients */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/30 dark:via-emerald-900/30 dark:to-teal-900/30"></div>
          <motion.div
            className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.3),transparent_70%)]"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.2, 0.3, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          ></motion.div>
        </div>

        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">
            <motion.div
              className="lg:col-span-6"
              style={{
                opacity: heroOpacity,
                y: heroY,
                scale: heroScale
              }}
            >
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, ease: "easeOut" }}
                className="relative"
              >
                {/* Decorative element */}
                <div className="absolute -left-6 -top-6 w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-emerald-300 opacity-20 blur-2xl"></div>

                <h1 className="text-5xl font-bold mb-6 leading-tight bg-clip-text text-transparent bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 dark:from-green-400 dark:via-emerald-400 dark:to-teal-400">
                  Take control of your <span className="relative inline-block">
                    energy costs
                    <svg className="absolute -bottom-2 left-0 w-full h-2 text-emerald-400/30" viewBox="0 0 100 12" preserveAspectRatio="none">
                      <path d="M0,0 Q50,12 100,0" stroke="currentColor" strokeWidth="8" fill="none" />
                    </svg>
                  </span>
                </h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
                  className="text-slate-700 dark:text-slate-200 text-xl mb-8 leading-relaxed font-medium"
                >
                  ALENSOFT EMS delivers comprehensive energy management solutions that help businesses monitor, control,
                  and <span className="text-emerald-600 dark:text-emerald-400 font-semibold">optimize their energy consumption</span> with precision.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
                  className="flex flex-wrap gap-5"
                >
                  <Button
                    className="bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 text-white shadow-lg font-semibold text-base px-8 py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1"
                    style={{ boxShadow: "0 10px 30px -5px rgba(16, 185, 129, 0.5)" }}
                    onClick={handleEnquiryClick}
                  >
                    <span className="mr-2">ENQUIRE NOW</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </Button>

                  <Button
                    variant="outline"
                    className="border-2 border-green-500 text-green-600 hover:bg-green-50 dark:text-green-300 dark:border-green-700 dark:hover:bg-green-900/30 px-8 py-6 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1 relative group"
                    onClick={handleBrochureClick}
                  >
                    {/* Add a subtle glow effect on hover */}
                    <div className="absolute -inset-1 bg-green-500/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center">
                      <span className="mr-2">VIEW PDF BROCHURE</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 2 0 002 2h8a2 2 2 0 002-2V7.414A2 2 2 0 0015.414 6L12 2.586A2 2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-6 relative"
            >
              <img
                src="/Alencloud.png"
                alt="ALENSOFT EMS Dashboard"
                className="w-full h-auto object-cover rounded-3xl shadow-2xl transform hover:scale-[1.02] transition-all duration-700"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section - Enhanced with Interactive Cards */}
      <section className="py-24 relative overflow-hidden">
        {/* Gradient background with animated overlay */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"></div>
          <div className="absolute inset-0 opacity-30 bg-[linear-gradient(60deg,transparent_0%,rgba(16,185,129,0.1)_20%,transparent_30%)]" style={{ backgroundSize: "200% 200%", animation: "gradient-shift 15s ease infinite" }}></div>
        </div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block">
              Our Impact By Numbers
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10">
            {statsData.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="relative group"
              >
                <div className="absolute -inset-3 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl relative overflow-hidden backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 h-full flex flex-col justify-between transform group-hover:translate-y-[-8px] transition-transform duration-500"
                  style={{
                    boxShadow: "0 20px 40px rgba(16, 185, 129, 0.08), 0 10px 20px rgba(16, 185, 129, 0.06)"
                  }}
                >
                  {/* Colored gradient blob in corner */}
                  <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full"
                    style={{
                      background: `radial-gradient(circle, ${stat.color1}20, transparent 70%)`,
                      filter: "blur(25px)"
                    }}>
                  </div>

                  <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                    style={{
                      background: `radial-gradient(circle, ${stat.color2}20, transparent 70%)`,
                      filter: "blur(25px)"
                    }}>
                  </div>

                  <div className="text-4xl mb-6"
                    style={{
                      color: stat.color1
                    }}>
                    {stat.icon}
                  </div>

                  <div>
                    <div className="text-5xl font-bold mb-2 bg-clip-text text-transparent relative z-10"
                      style={{
                        backgroundImage: `linear-gradient(135deg, ${stat.color1}, ${stat.color2})`
                      }}>
                      <CountUp
                        to={stat.number}
                        from={0}
                        duration={2.5}
                        delay={0.5 + index * 0.2}
                        className="inline-block" onStart={undefined} onEnd={undefined}                      />
                      <span>{stat.suffix}</span>
                    </div>
                    <div className="text-xl font-semibold text-slate-700 dark:text-slate-200 mb-3">{stat.text}</div>
                    <div className="text-sm text-slate-500 dark:text-slate-400">{stat.description}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Energy Management Steps - Enhanced with Modern 3D Timeline */}
      <section className="py-24 relative overflow-hidden">
        {/* Dynamic animated background */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20"></div>

          {/* Animated flowing particles effect */}
          <motion.div
            className="absolute inset-0 opacity-10"
            animate={{
              backgroundPosition: ['0% 0%', '100% 100%'],
            }}
            transition={{
              repeat: Infinity,
              repeatType: "reverse",
              duration: 20,
              ease: "linear"
            }}
            style={{
              backgroundImage: "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2310b981' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
              backgroundSize: "60px 60px"
            }}
          />

          {/* Glowing orbs that float around */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-emerald-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-teal-400/20 rounded-full blur-3xl animate-pulse"
            style={{ animationDelay: "2s", animationDuration: "7s" }}></div>
        </div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block"
            >
              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-600 to-teal-600 dark:from-emerald-400 dark:to-teal-400 inline-block">
                Energy Management Pathway
              </h2>
              <motion.div
                initial={{ width: 0 }}
                whileInView={{ width: "100%" }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="h-1 bg-gradient-to-r from-emerald-500 to-teal-400 mt-2 rounded-full"
              />
            </motion.div>
            <motion.span
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="block text-lg font-medium mt-3 text-slate-600 dark:text-slate-300"
            >
              Transform your resource management in four revolutionary steps
            </motion.span>
          </motion.div>

          {/* 3D Timeline View */}
          <div className="perspective-1000">
            <div className="relative max-w-6xl mx-auto">
              {/* Removed the background central line */}

              {managementSteps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-50px" }}
                  transition={{ duration: 0.7, delay: index * 0.2 }}
                  className="mb-20 last:mb-0 relative"
                >
                  {/* Position marker removed */}

                  <div className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-x-12 gap-y-8`}>
                    {/* 3D Step Number with Floating Animation - Enhanced for better visibility */}
                    <motion.div
                      className="flex-shrink-0 relative w-28 h-28 perspective-1000 z-10"
                      whileHover={{ scale: 1.05, rotateY: 20, rotateX: -10 }}
                    >
                      {/* 3D layered effect with improved contrast */}
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute inset-0 rounded-2xl flex items-center justify-center"
                          style={{
                            backgroundColor: i === 0 ? step.color : `${step.color}${i === 0 ? '' : Math.floor(90 - i * 15).toString(16)}`,
                            opacity: i === 0 ? 1 : 0.9 - (i * 0.15),
                            transform: `translateZ(${-(i * 2)}px)`,
                            boxShadow: i === 0 ? `0 15px 30px -5px ${step.color}80, 0 0 0 2px rgba(255,255,255,0.6)` : 'none'
                          }}
                          whileHover={{
                            translateZ: -(i * 3),
                          }}
                          transition={{ duration: 0.3 }}
                        >
                          {i === 0 && (
                            <div className="text-white flex flex-col items-center">
                              <span className="text-5xl font-bold">{step.number}</span>
                              <span className="text-xs uppercase tracking-wider mt-1 font-medium">STEP</span>
                            </div>
                          )}
                        </motion.div>
                      ))}

                      {/* Enhanced pulsing ring animation */}
                      <motion.div
                        className="absolute inset-0 rounded-2xl border-2"
                        style={{ borderColor: step.color }}
                        animate={{
                          scale: [1, 1.1, 1],
                          opacity: [0.7, 0.3, 0.7]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />

                      {/* Add an extra glow effect for better visibility */}
                      <div
                        className="absolute -inset-2 rounded-2xl opacity-50"
                        style={{
                          background: `radial-gradient(circle at center, ${step.color}40, transparent 70%)`,
                          filter: "blur(8px)"
                        }}
                      ></div>
                    </motion.div>

                    {/* Content Card with 3D Hover Effect */}
                    <motion.div
                      className={`flex-1 transform perspective-1000`}
                      initial={{ rotateX: 0, rotateY: 0 }}
                      whileHover={{ rotateY: index % 2 === 0 ? -5 : 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <div
                        className="relative group bg-gradient-to-br from-white to-slate-50/80 dark:from-slate-800 dark:to-slate-900/80 backdrop-blur-md rounded-2xl p-8 shadow-2xl overflow-hidden border border-emerald-100/50 dark:border-emerald-900/30"
                        style={{ transformStyle: "preserve-3d" }}
                      >
                        {/* Background gradient that moves on hover */}
                        <div
                          className="absolute inset-0 opacity-50 group-hover:opacity-80 transition-opacity duration-500"
                          style={{
                            background: `radial-gradient(circle at 50% 50%, ${step.color}30, transparent 70%)`,
                            filter: "blur(20px)"
                          }}
                        />

                        {/* Dynamic border glow on hover */}
                        <motion.div
                          className="absolute inset-0 opacity-0 group-hover:opacity-100 rounded-2xl pointer-events-none transition-opacity duration-300"
                          style={{
                            boxShadow: `0 0 20px 1px ${step.color}40, inset 0 0 20px 1px ${step.color}40`
                          }}
                        />

                        {/* Icon with 3D float effect */}
                        <motion.div
                          className="mb-6 relative w-16 h-16 mx-auto md:mx-0"
                          animate={{
                            y: [0, -8, 0],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          style={{ transformStyle: "preserve-3d", transform: "translateZ(20px)" }}
                        >
                          <div
                            className="absolute inset-0 rounded-full"
                            style={{
                              background: `radial-gradient(circle at 50% 50%, ${step.color}50, ${step.color}20)`,
                              filter: "blur(10px)"
                            }}
                          />
                          <div className="absolute inset-0 rounded-full flex items-center justify-center">
                            <span className="text-4xl" style={{ color: step.color }}>{step.icon}</span>
                          </div>
                        </motion.div>

                        {/* Title with shine effect on hover */}
                        <div className="overflow-hidden relative mb-3">
                          <motion.h3
                            className={`text-3xl font-bold ${index % 2 === 0 ? 'text-left' : 'text-right'}`}
                            style={{ color: step.color }}
                          >
                            {step.title}
                            <div
                              className="absolute top-0 bottom-0 w-40 opacity-0 group-hover:opacity-100 group-hover:translate-x-full transition-all duration-1000 pointer-events-none"
                              style={{
                                background: "linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent)",
                                transform: "skewX(-20deg)",
                                left: "-50px"
                              }}
                            />
                          </motion.h3>
                        </div>

                        {/* Description */}
                        <p
                          className={`text-lg text-slate-700 dark:text-slate-300 leading-relaxed ${index % 2 === 0 ? 'text-left' : 'text-right'}`}
                          style={{ transform: "translateZ(5px)" }}
                        >
                          {step.description}
                        </p>

                        {/* Interactive element */}
                        <motion.div
                          whileHover={{ scale: 1.03 }}
                          whileTap={{ scale: 0.98 }}
                          className="mt-6 rounded-xl p-4 backdrop-blur-sm relative overflow-hidden group/feature"
                          style={{
                            backgroundColor: `${step.color}10`,
                            borderLeft: `3px solid ${step.color}`
                          }}
                        >
                          {/* Arrow indicator that animates on hover */}
                          <motion.div
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-0 group-hover/feature:opacity-100 transition-opacity"
                            animate={{ x: [0, 5, 0] }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill={step.color}>
                              <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </motion.div>

                          <div className={`flex items-center gap-3 ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                            <span className="font-medium text-slate-800 dark:text-slate-200">Key Benefit:</span>
                            <span className="text-slate-700 dark:text-slate-300">
                              {index === 0 && "Comprehensive data collection"}
                              {index === 1 && "Clear performance benchmarking"}
                              {index === 2 && "Significant cost reduction"}
                              {index === 3 && "Actionable business insights"}
                            </span>
                          </div>
                        </motion.div>

                        {/* Progress indicator */}
                        <div className="absolute bottom-4 right-4 flex items-center gap-1">
                          {[...Array(4)].map((_, i) => (
                            <div
                              key={i}
                              className="w-2 h-2 rounded-full"
                              style={{
                                backgroundColor: i <= step.number - 1 ? step.color : 'rgba(203, 213, 225, 0.4)'
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              ))}

              {/* Completion badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 1.8 }}
                className="mt-12 text-center"
              >
                <div className="inline-block rounded-full px-6 py-2 backdrop-blur-sm border border-emerald-100 dark:border-emerald-800/50 bg-emerald-50/50 dark:bg-emerald-900/20">
                  <span className="text-emerald-700 dark:text-emerald-400 font-medium flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Complete this pathway to optimize your energy management
                  </span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Comprehensive Reporting - Enhanced with Modern Card Design */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-green-50/80 to-emerald-50/60 dark:from-green-900/30 dark:via-green-900/20 dark:to-emerald-900/10 -z-10"></div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 inline-block">
              Comprehensive Reporting
            </h2>
            <span className="block text-lg font-medium mt-3 text-slate-600 dark:text-slate-300">
              Make data-driven decisions with powerful analytics
            </span>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white dark:bg-slate-800/90 backdrop-blur-md rounded-3xl p-12 shadow-2xl relative border border-slate-200/50 dark:border-slate-700/50"
            style={{
              boxShadow: "0 30px 60px rgba(16, 185, 129, 0.15), 0 15px 30px rgba(16, 185, 129, 0.1)"
            }}
          >
            {/* Subtle pattern overlay */}
            <div className="absolute inset-0 opacity-5 -z-10 rounded-3xl"
              style={{
                backgroundImage: "radial-gradient(circle at 20px 20px, rgba(16, 185, 129, 0.3) 2px, transparent 0)",
                backgroundSize: "20px 20px"
              }}
            ></div>

            {/* Reporting visualization with direct container */}
            <div className="mb-14 relative">
              {/* Glass effect overlay */}
              <div className="absolute inset-0 bg-white/10 dark:bg-emerald-900/10 backdrop-blur-[1px] rounded-xl z-10 opacity-0 hover:opacity-100 transition-opacity duration-700"></div>

              {/* Subtle glow effect on hover */}
              <div className="absolute -inset-2 bg-gradient-to-r from-emerald-500/0 via-emerald-500/10 to-emerald-500/0 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-1000 z-0"></div>

              <motion.img
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                src="/background_images/reporting-2.png"
                alt="ALENSOFT EMS Reporting Dashboard"
                className="w-full rounded-xl shadow-lg border border-slate-100 dark:border-slate-700 transition-all duration-700 hover:scale-[1.02] hover:shadow-emerald-500/20 hover:shadow-2xl"
              />

              {/* Interactive badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: 0.6 }}
                className="absolute bottom-4 right-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg border border-emerald-100 dark:border-emerald-800/50 flex items-center gap-2"
              >
                <div className="w-2 h-2 bg-emerald-500 rounded-full relative">
                  <div className="absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-75"></div>
                </div>
                <span className="text-xs font-medium text-emerald-800 dark:text-emerald-300">Live Data Monitoring</span>
              </motion.div>
            </div>

            {/* Feature cards in grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { icon: "📈", title: "Real-time Monitoring", description: "Track energy consumption with millisecond precision and receive instant alerts on abnormal patterns", color: "green" },
                { icon: "📊", title: "Custom Dashboards", description: "Build personalized reports and visualizations tailored to your specific business needs", color: "emerald" },
                { icon: "📱", title: "Universal Access", description: "Access insights securely from any device, anywhere with our responsive web and mobile interface", color: "teal" }
              ].map((item, index) => (
                <FeatureCard
                  key={index}
                  icon={item.icon}
                  title={item.title}
                  description={item.description}
                  color={item.color}
                  delay={0.3 + index * 0.1}
                  index={index}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features & Reports - Enhanced with Interactive Cards */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/30 dark:to-teal-900/30 -z-10"></div>

        {/* Animated background element */}
        <motion.div
          className="absolute inset-0 -z-10 opacity-10"
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%'],
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 30,
            ease: "linear"
          }}
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2310b981" fill-opacity="0.2"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'
          }}
        />

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-600 to-teal-600 dark:from-emerald-400 dark:to-teal-400 inline-block">
              FEATURES & REPORTS
            </h2>
            <span className="block text-lg font-medium mt-3 text-slate-600 dark:text-slate-300">
              Designed with your business needs in mind
            </span>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-teal-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          {/* Key Features Grid - Interactive Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                whileHover={{
                  y: -10,
                  transition: { duration: 0.3 }
                }}
                className="relative group"
              >
                {/* Hover glow effect */}
                <div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-emerald-500/20 to-teal-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-500 group-hover:duration-200"></div>

                <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl border border-slate-200/50 dark:border-slate-700/50 relative h-full flex flex-col items-center text-center z-10 backdrop-blur-sm">
                  {/* Icon with base and hover states */}
                  <div className="relative flex items-center justify-center mb-6 transition-transform duration-500 transform group-hover:scale-110">
                    {/* Glow behind icon */}
                    <div className="absolute w-16 h-16 rounded-full bg-emerald-400/10 group-hover:bg-emerald-400/20 blur-xl transition-all duration-500"></div>

                    {/* Icon */}
                    <div className="relative z-20 text-5xl text-emerald-500 dark:text-emerald-400">{feature.icon}</div>
                  </div>

                  {/* Title with hover effect */}
                  <h3 className="text-xl font-bold text-slate-800 dark:text-slate-200 mb-3 transition-colors duration-300 group-hover:text-emerald-600 dark:group-hover:text-emerald-400">{feature.title}</h3>

                  {/* Description that appears on hover */}
                  <p className="text-slate-600 dark:text-slate-400 text-sm opacity-0 max-h-0 overflow-hidden transition-all duration-300 group-hover:opacity-100 group-hover:max-h-24">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Available Reports Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="rounded-3xl p-12 relative bg-white dark:bg-slate-800/90 backdrop-blur-md border border-slate-200/50 dark:border-slate-700/50"
            style={{
              boxShadow: "0 30px 60px rgba(16,185,129,0.15), 0 10px 30px rgba(16,185,129,0.1)"
            }}
          >
            {/* Decorative gradient blobs */}
            <div className="absolute -top-10 -left-10 w-40 h-40 bg-emerald-400/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-teal-400/10 rounded-full blur-3xl"></div>

            {/* Subtle pattern */}
            <div className="absolute inset-0 opacity-5 -z-10 rounded-3xl"
              style={{
                backgroundImage: "radial-gradient(circle at 20px 20px, rgba(16, 185, 129, 0.2) 2px, transparent 0)",
                backgroundSize: "20px 20px"
              }}
            ></div>

            <h3 className="text-3xl font-bold mb-10 text-center bg-clip-text text-transparent bg-gradient-to-r from-teal-600 to-emerald-600 dark:from-teal-400 dark:to-emerald-400">
              Available Reports
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {reports.map((report, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  className={`p-8 rounded-2xl bg-gradient-to-br from-white to-${report.color}-50 dark:from-slate-800 dark:to-${report.color}-900/20 shadow-xl border border-${report.color}-100 dark:border-${report.color}-800/50 relative group overflow-hidden`}
                  whileHover={{
                    y: -10,
                    transition: { duration: 0.2 }
                  }}
                >
                  {/* Background pattern that appears on hover */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500"
                    style={{
                      backgroundImage: `radial-gradient(circle at 20px 20px, rgba(16, 185, 129, 0.4) 4px, transparent 0)`,
                      backgroundSize: "30px 30px"
                    }}
                  ></div>

                  {/* Icon with glow effect */}
                  <div className="relative inline-block mb-5">
                    <div className={`absolute inset-0 bg-${report.color}-400/20 rounded-full blur-xl transform scale-150 transition-all duration-300 group-hover:bg-${report.color}-400/30`}></div>
                    <div className={`text-5xl text-${report.color}-500 dark:text-${report.color}-400 relative z-10`}>{report.icon}</div>
                  </div>

                  <h4 className={`font-bold text-xl mb-4 text-slate-800 dark:text-slate-200 group-hover:text-${report.color}-600 dark:group-hover:text-${report.color}-400 transition-colors duration-300`}>
                    {report.title}
                  </h4>

                  <p className="text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-colors duration-300 leading-relaxed">
                    {report.description}
                  </p>

                  {/* View Sample button that appears on hover */}
                  <div className="mt-6 overflow-hidden h-0 group-hover:h-10 transition-all duration-300 opacity-0 group-hover:opacity-100">
                    <button className={`mt-2 flex items-center font-medium text-${report.color}-600 dark:text-${report.color}-400`}>
                      <span>View Sample</span>
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* System Architecture - Enhanced with Blended Image Design */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 -z-10"></div>

        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-400 dark:to-teal-400 inline-block">
              ALENSOFT EMS
            </h2>
            <span className="block text-lg font-medium mt-3 text-slate-600 dark:text-slate-300">
              Advanced System Architecture
            </span>
            <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-teal-400 mx-auto mt-4 rounded-full"></div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            {/* Decorative elements */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-green-400/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-teal-400/10 rounded-full blur-3xl"></div>

            {/* Image directly blended with UI, not in a container */}
            <div className="relative mb-14">
              <motion.img
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                src="/background_images/Schematic-EMS-on-Cloud.jpg"
                alt="EMS System Architecture"
                className="w-full rounded-2xl shadow-xl"
                style={{
                  maxWidth: "100%"
                }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10 backdrop-blur-sm">
              {[
                { icon: "🔌", title: "Enterprise Integration", description: "Seamlessly connects with your existing infrastructure and third-party systems" },
                { icon: "📊", title: "Bi-directional Data Flow", description: "Enables both monitoring and control capabilities for complete system management" },
                { icon: "🛡️", title: "Defense-grade Security", description: "End-to-end encryption and multi-level authentication protects sensitive data" }
              ].map((item, index) => (
                <FeatureCard
                  key={index}
                  icon={item.icon}
                  title={item.title}
                  description={item.description}
                  color="green"
                  delay={0.3 + index * 0.1}
                  index={index}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Contact Section - Following the provided design */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-green-50/60 to-teal-50/40 dark:from-emerald-900/30 dark:via-green-900/20 dark:to-teal-900/10 -z-10"></div>

        {/* Subtle animated dots background */}
        <motion.div
          className="absolute inset-0 -z-10 opacity-10"
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%'],
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 20,
            ease: "linear"
          }}
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%2310b981" fill-opacity="0.3"%3E%3Ccircle cx="10" cy="10" r="2"/%3E%3C/g%3E%3C/svg%3E")'
          }}
        />

        <div className="container mx-auto px-4 max-w-6xl">
          {/* Need More Information Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="rounded-3xl p-12 relative bg-gradient-to-br from-green-50 to-emerald-50/80 dark:from-green-900/20 dark:to-emerald-900/10 backdrop-blur-md border border-green-100/50 dark:border-green-800/30 shadow-2xl overflow-hidden"
          >
            {/* Decorative elements */}
            <div className="absolute -top-20 -right-20 w-60 h-60 bg-green-400/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-60 h-60 bg-emerald-400/10 rounded-full blur-3xl"></div>

            <div className="relative z-10 text-center max-w-3xl mx-auto">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-4xl font-bold mb-6 text-slate-800 dark:text-slate-100"
              >
                Need More Information?
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-lg mb-10 text-slate-700 dark:text-slate-300"
              >
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other
                details you need about <span className="font-semibold text-emerald-700 dark:text-emerald-500">ALENSOFT Energy Management Systems</span>.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex justify-center"
              >
                <motion.button
                  whileHover={{ scale: 1.05, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-4 px-10 rounded-xl flex items-center gap-3 shadow-xl"
                  style={{ boxShadow: "0 10px 30px rgba(16, 185, 129, 0.3)" }}
                  onClick={handleContactSalesClick}
                >
                  <span>Contact Our Experts</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H3a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </motion.button>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default OnPremiseSystems;