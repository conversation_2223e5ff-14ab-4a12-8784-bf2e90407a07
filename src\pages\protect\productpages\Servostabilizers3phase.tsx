import React from 'react';
import { CheckCircle, Zap, Shield, Wrench, Phone, Mail, MapPin, Star, Download, Info } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const ServoStabilizers3Phase = () => {

  const features = [
    {
      icon: <CheckCircle className="w-8 h-8" />,
      title: "True RMS Correction",
      items: [
        "Microprocessor based control system for measurement and correction",
        "Digital Voltage, Current & Frequency display",
        "MCU sensing and correction technology",
        "High precision voltage regulation"
      ]
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "High Efficiency Design",
      items: [
        "Stabilizers designed to rated capacity",
        "Efficiency > 98%",
        "Optimal performance under all load conditions",
        "Energy saving operation"
      ]
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Complete Protection",
      items: [
        "Short Circuit, Under/Over voltage protection",
        "Electronic CT based Overload trip",
        "Single Phasing & Phase Reversal trip",
        "Built-in Spike Suppressor"
      ]
    },
    {
      icon: <Wrench className="w-8 h-8" />,
      title: "After Sales Support",
      items: [
        "'No Questions-Asked' guarantee",
        "Wide service network covering 100+ locations",
        "Service response within 6-24 hours",
        "Comprehensive maintenance support"
      ]
    }
  ];

  const specifications = [
    { parameter: "Rating (kVA)", "Air Cooled": "3 kVA - 150 kVA", "Oil Cooled": "3 kVA - 600 kVA" },
    { parameter: "Type of Cooling", "Air Cooled": "Air Cooled", "Oil Cooled": "Oil Cooled" },
    { parameter: "Input Voltage Range", "Air Cooled": "310V-460V / 340V-420V", "Oil Cooled": "310V-460V / 340V-420V" },
    { parameter: "Output Voltage", "Air Cooled": "415V±1% / 380V±1% / 400V±1%", "Oil Cooled": "415V±1% / 380V±1% / 400V±1%" },
    { parameter: "Output Regulation", "Air Cooled": "±1% of nominal voltage", "Oil Cooled": "±1% of nominal voltage" },
    { parameter: "Input Frequency", "Air Cooled": "50 Hz ± 5 Hz", "Oil Cooled": "50 Hz ± 5 Hz" },
    { parameter: "Efficiency", "Air Cooled": "> 98%", "Oil Cooled": "> 98%" },
    { parameter: "Load Power Factor Effect", "Air Cooled": "Nil", "Oil Cooled": "Nil" },
    { parameter: "Waveform Distortion", "Air Cooled": "Nil", "Oil Cooled": "Nil" },
    { parameter: "Servo Control Type", "Air Cooled": "MCU Based", "Oil Cooled": "MCU Based" },
    { parameter: "Servo Motor Drive", "Air Cooled": "True band drive AC motor", "Oil Cooled": "True band drive AC motor" },
    { parameter: "Under/Over Voltage Cutoff", "Air Cooled": "Electronic with LED display", "Oil Cooled": "Electronic with LED display" },
    { parameter: "Overload Protection", "Air Cooled": "CT based @ 110%", "Oil Cooled": "CT based @ 110%" },
    { parameter: "Short Circuit Protection", "Air Cooled": "MCB/MCCB/HRC", "Oil Cooled": "MCB/MCCB/HRC" },
    { parameter: "Single Phasing Prevention", "Air Cooled": "Provided (All models)", "Oil Cooled": "Provided (All models)" },
    { parameter: "Phase Reversal Trip", "Air Cooled": "Provided (All models)", "Oil Cooled": "Provided (All models)" },
    { parameter: "Stabilizer Bypass", "Air Cooled": "Up to 50kVA (Optional >50kVA)", "Oil Cooled": "Up to 50kVA (Optional >50kVA)" },
    { parameter: "Transient Suppression", "Air Cooled": "3-limb Inductance", "Oil Cooled": "3-limb Inductance" },
    { parameter: "Emergency Off Switch", "Air Cooled": "Provided (All models)", "Oil Cooled": "Provided (All models)" },
    { parameter: "Display Type", "Air Cooled": "3 line LCD panel", "Oil Cooled": "3 line LCD panel" },
    { parameter: "Parameters Displayed", "Air Cooled": "Input/Output V, I, F", "Oil Cooled": "Input/Output V, I, F" },
    { parameter: "Input/Output Terminations", "Air Cooled": "Bolted/Busbar", "Oil Cooled": "Bolted/Busbar" },
    { parameter: "Standards", "Air Cooled": "IS 9815 Part-1:1981", "Oil Cooled": "IS 9815 Part-1:1981" }
  ];

  return (
    <PageLayout
      title="Servo Stabilizers - 3 Phase"
      subtitle="Advanced three-phase voltage protection solutions"
      category="protect"
    >
      <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 font-sans -mx-6 -mt-24 pt-16">
        {/* Hero Content Section - Integrated with PageLayout */}
        <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white relative">
          {/* Background Image with Transparency */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
            style={{
              backgroundImage: "url('https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
            }}
          ></div>
          <div className="w-full px-6 md:px-12 lg:px-16 py-16 md:py-24 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-8">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Servo Controlled Voltage Stabilizers
                  <span className="block text-blue-300 text-3xl md:text-4xl mt-2">3 Phase</span>
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                  Advanced three-phase voltage protection with microprocessor-based control, providing stable power quality for industrial applications with True RMS correction and complete protection features.
                </p>
                <div className="flex items-center justify-center gap-6">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-7 h-7 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-4 text-xl text-blue-100 font-medium">Trusted Power Quality</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-6 justify-center pt-4">
                  <button
                    onClick={() => {
                      // Redirect to Sales page for quote
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Mail className="w-6 h-6 inline mr-3" />
                    Get Quote
                  </button>
                  <button
                    onClick={() => {
                      // Create a link to download the PDF brochure
                      const link = document.createElement('a');
                      link.href = '/brochures/servo-stabilizers-3phase-brochure.pdf';
                      link.download = 'Servo-Stabilizers-3Phase-Brochure.pdf';
                      link.click();
                    }}
                    className="bg-white text-blue-800 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <Download className="w-6 h-6 inline mr-3" />
                    Download Brochure
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Overview Section - Full Width */}
      <div className="w-full bg-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
              Advanced Three-Phase Power Protection
            </h2>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed text-justify max-w-6xl mx-auto">
              Our Servo Controlled Voltage Stabilizers provide superior three-phase voltage regulation with microprocessor-based control systems, ensuring stable power quality for industrial applications. Built with True RMS correction and comprehensive protection features.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-blue-200">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-4 shadow-lg mb-4">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
                </div>
                <ul className="space-y-4">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-base text-gray-800 leading-relaxed text-justify font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Full Width */}
      <div className="w-full bg-gradient-to-br from-blue-900 to-blue-800">
        <div className="w-full px-6 md:px-12 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Specifications
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed text-justify max-w-4xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability for both air-cooled and oil-cooled variants.
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-700 to-blue-800">
                    <th className="px-8 py-6 text-left text-lg font-bold text-white border-b-2 border-blue-600">
                      Parameter
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      Air Cooled
                    </th>
                    <th className="px-8 py-6 text-center text-lg font-bold text-white border-b-2 border-blue-600">
                      Oil Cooled
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'} hover:bg-blue-100 transition-colors duration-200`}>
                      <td className="px-8 py-5 text-base font-bold text-gray-900 border-b border-blue-200 bg-gradient-to-r from-blue-100 to-blue-50">
                        {spec.parameter}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['Air Cooled']}
                      </td>
                      <td className="px-8 py-5 text-base text-gray-800 border-b border-blue-200 text-center font-medium">
                        {spec['Oil Cooled']}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-blue-50 px-8 py-6 border-t-2 border-blue-200">
              <p className="text-base text-gray-800 font-medium flex items-center justify-center">
                <Info className="w-6 h-6 inline mr-3 text-blue-600" />
                All specifications are subject to standard tolerances and testing conditions as per IS 9815 Part-1:1981 standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section - Full Width */}
      <div className="w-full bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 text-white">
        <div className="w-full px-6 md:px-12 lg:px-16 py-20">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              Ready for Industrial Power Protection?
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed text-justify">
              Get in touch with our experts to find the perfect three-phase servo stabilizer solution for your industrial needs. We provide comprehensive support from consultation to installation and maintenance.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-blue-600 text-white px-10 py-5 rounded-xl font-bold text-xl hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                <Mail className="w-6 h-6 inline mr-3" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
};

export default ServoStabilizers3Phase;