import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate, useLocation } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronRight,
  ChevronLeft,
  Thermometer,
  FileText,
  Mail,
  ArrowRight,
  ZoomIn,
  Eye,
  Zap,
  Shield,
  Activity,
  BarChart3,
  Search,
  Camera,
  Battery,
  Flame,
  X,
  Check,
  Grid
} from "lucide-react";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-amber-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-amber-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Add animation styles
const animationStyles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }
`;

// Add styles to document
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = animationStyles;
  document.head.appendChild(style);
}

// Thermal Imagers data - ordered from lowest to highest resolution/capability
const thermalImagers = [
  // Entry-level and Pocket Series
  {
    id: "tc-s030",
    name: "TC S030",
    description: "Entry-level thermal imager with touch screen interface",
    features: [
      "IR Resolution: 96 × 96 (9,216 pixels)",
      "SuperIR Resolution: Yes, on captured images & live view",
      "Temperature Range: -20°C to 350°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 50° × 50°",
      "NETD: < 50 mK",
      "Focus: Fixed Focus & minimum focus of 0.1m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "96 × 96",
      superIRResolution: "Yes, on captured images & live view",
      temperatureRange: "-20°C to 350°C",
      accuracy: "±2°C, ±2%",
      fov: "50° × 50°",
      netd: "< 50 mK",
      focus: "Fixed Focus & minimum focus of 0.1m",
      imageFrequency: "25 Hz"
    },
    image: "/thermal-measurement/TC-S030.png",
    battery: "4 hours (Built-in)",
    memory: "Built-in 4GB",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "640 × 480 (0.3 MP)",
    display: "320 × 240 Resolution, 3.5\" LCD touch screen",
    category: "compact",
    series: "Compact Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 96 × 96 (9,216 pixels)",
        "SuperIR Resolution: Yes, on captured images & live view",
        "NETD (Thermal Sensitivity): < 50 mK",
        "Field of View: 50° × 50°",
        "Spatial Resolution (IFOV): 8.89 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed Focus & minimum focus of 0.1m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending"
      ],
      measurement: [
        "Temperature Range: -20°C to 350°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot Spot Detection",
        "Color Palettes: Iron, Rainbow, Grey"
      ],
      storage: [
        "Storage Medium: Built-in 4GB",
        "Image Storage Capacity: up to 30,000 images",
        "Video Storage Capacity: up to 20 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 320 × 240 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 640 × 480 (0.3 MP)",
        "Battery: Li-ion, 4 hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, WiFi"
      ]
    }
  },
  {
    id: "tc-e050",
    name: "TC E050",
    description: "Pocket-sized thermal imager with extended battery life",
    features: [
      "IR Resolution: 96 × 96 (9,216 pixels)",
      "SuperIR Resolution: 240 × 240",
      "Temperature Range: -20°C to 550°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 50° × 50°",
      "NETD: < 50 mK",
      "Focus: Fixed & minimum distance of 0.1m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "96 × 96",
      superIRResolution: "240 × 240",
      temperatureRange: "-20°C to 550°C",
      accuracy: "±2°C, ±2%",
      fov: "50° × 50°",
      netd: "< 50 mK",
      focus: "Fixed & minimum distance of 0.1m",
      imageFrequency: "25 Hz",
      spatialResolution: "8.89 mrad"
    },
    image: "/thermal-measurement/TC-E050.png",
    battery: "8 hours (Built-in)",
    memory: "Built-in 4GB flash memory",
    imageMode: "Thermal/Visual/Fusion",
    visualCamera: "640 × 480 (0.3 MP)",
    display: "240 × 320 Resolution, 2.4\" LCD Screen",
    category: "compact",
    series: "Pocket Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 96 × 96 (9,216 pixels)",
        "SuperIR Resolution: 240 × 240",
        "NETD (Thermal Sensitivity): < 50 mK",
        "Field of View: 50° × 50°",
        "Spatial Resolution (IFOV): 8.89 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed & minimum distance of 0.1m",
        "Image Modes: Thermal/Visual/Fusion"
      ],
      measurement: [
        "Temperature Range: -20°C to 550°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot Spot Detection",
        "Color Palettes: Iron, Rainbow, Grey, Hot Metal"
      ],
      storage: [
        "Storage Medium: Built-in 4GB flash memory",
        "Image Storage Capacity: up to 18,000 images",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 240 × 320 Resolution, 2.4\" LCD Screen",
        "Visual Camera: 640 × 480 (0.3 MP)",
        "Battery: Li-ion, 8 hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, Laser pointer"
      ]
    }
  },
  {
    id: "ma-250",
    name: "MA 250",
    description: "Smartphone attachment thermal imager for mobile applications",
    features: [
      "IR Resolution: 256 × 192 (49,152 pixels)",
      "SuperIR Resolution: Yes, on captured images & live view",
      "Temperature Range: -20°C to 400°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 50° × 37.2°",
      "NETD: < 40 mK",
      "Focus: Fixed & minimum distance 0.2m",
      "Image Frequency: 50 Hz"
    ],
    specifications: {
      irResolution: "256 × 192",
      superIRResolution: "Yes, on captured images & live view",
      temperatureRange: "-20°C to 400°C",
      accuracy: "±2°C, ±2%",
      fov: "50° × 37.2°",
      netd: "< 40 mK",
      focus: "Fixed & minimum distance 0.2m",
      imageFrequency: "50 Hz"
    },
    image: "/thermal-measurement/ma-250.png",
    imageMode: "Thermal/PIP",
    communication: "USB-C Android, USB-C iOS, Lightning iOS (Includes a Lightning to USB-C adapter)",
    category: "mobile",
    series: "Mobile Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 256 × 192 (49,152 pixels)",
        "SuperIR Resolution: Yes, on captured images & live view",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 50° × 37.2°",
        "Focus: Fixed & minimum distance 0.2m",
        "Image Frequency: 50 Hz",
        "Image Modes: Thermal/PIP"
      ],
      measurement: [
        "Temperature Range: -20°C to 400°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Detection",
        "Color Palettes: Iron, Rainbow, Grey, Lava, Arctic"
      ],
      storage: [
        "Storage Medium: Uses smartphone storage",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: Uses smartphone display",
        "Communication Interface: USB-C Android, USB-C iOS, Lightning iOS (Includes a Lightning to USB-C adapter)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Power: Supplied by smartphone"
      ]
    }
  },
  // Compact Series - mid-range
  {
    id: "tc-2150",
    name: "TC 2150",
    description: "Compact thermal imager with 2MP visual camera",
    features: [
      "IR Resolution: 192 × 144 (27,648 pixels)",
      "SuperIR Resolution: Yes, on captured images & live view",
      "Temperature Range: -20°C to 550°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 27.8° × 37.2°",
      "NETD: < 40 mK",
      "Focus: Fixed & minimum distance 0.3m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "192 × 144",
      superIRResolution: "Yes, on captured images & live view",
      temperatureRange: "-20°C to 550°C",
      accuracy: "±2°C, ±2%",
      fov: "27.8° × 37.2°",
      netd: "< 40 mK",
      focus: "Fixed & minimum distance 0.3m",
      imageFrequency: "25 Hz",
      spatialResolution: "3.0 mrad"
    },
     image: "/thermal-measurement/TC-2150.png",
    battery: "6 hours (Built-in)",
    memory: "Built-in 16 GB",
    imageMode: "Thermal/Visual/Fusion/PIP",
    visualCamera: "1600 x 1200 (2 MP)",
    display: "240 × 320 Resolution, 3.2\" LCD screen",
    category: "compact",
    series: "Compact Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 192 × 144 (27,648 pixels)",
        "SuperIR Resolution: Yes, on captured images & live view",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 27.8° × 37.2°",
        "Spatial Resolution (IFOV): 3.0 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed & minimum distance 0.3m",
        "Image Modes: Thermal/Visual/Fusion/PIP"
      ],
      measurement: [
        "Temperature Range: -20°C to 550°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis",
        "Color Palettes: Iron, Rainbow, Grey, Grey Inverted"
      ],
      storage: [
        "Storage Medium: Built-in 16 GB",
        "Image Storage Capacity: up to 90,000 images",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 240 × 320 Resolution, 3.2\" LCD screen",
        "Visual Camera: 1600 x 1200 (2 MP)",
        "Battery: Li-ion, 6 hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, WiFi"
      ]
    }
  },
  {
    id: "tc-2250",
    name: "TC 2250",
    description: "Compact thermal imager with enhanced display resolution",
    features: [
      "IR Resolution: 256 × 192 (49,152 pixels)",
      "SuperIR Resolution: Yes, on captured images & live view",
      "Temperature Range: -20°C to 550°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 37.2° × 50.0°",
      "NETD: < 40 mK",
      "Focus: Fixed & minimum distance 0.3m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "256 × 192",
      superIRResolution: "Yes, on captured images & live view",
      temperatureRange: "-20°C to 550°C",
      accuracy: "±2°C, ±2%",
      fov: "37.2° × 50.0°",
      netd: "< 40 mK",
      focus: "Fixed & minimum distance 0.3m",
      imageFrequency: "25 Hz",
      spatialResolution: "3.3 mrad"
    },
    image: "/thermal-measurement/TC-2250.png",
    battery: "6 hours (Built-in)",
    memory: "Built-in 16 GB",
    imageMode: "Thermal/Visual/Fusion/PIP",
    visualCamera: "1600 x 1200 (2 MP)",
    display: "480 × 640 Resolution, 3.2\" LCD screen",
    category: "compact",
    series: "Compact Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 256 × 192 (49,152 pixels)",
        "SuperIR Resolution: Yes, on captured images & live view",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 37.2° × 50.0°",
        "Spatial Resolution (IFOV): 3.3 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed & minimum distance 0.3m",
        "Image Modes: Thermal/Visual/Fusion/PIP"
      ],
      measurement: [
        "Temperature Range: -20°C to 550°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis",
        "Color Palettes: Iron, Rainbow, Grey, Hot Metal"
      ],
      storage: [
        "Storage Medium: Built-in 16 GB",
        "Image Storage Capacity: up to 35,000 images",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 480 × 640 Resolution, 3.2\" LCD screen",
        "Visual Camera: 1600 x 1200 (2 MP)",
        "Battery: Li-ion, 6 hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, WiFi, Bluetooth"
      ]
    }
  },
  {
    id: "tc-s240",
    name: "TC S240",
    description: "Compact thermal imager with auto-rotation display",
    features: [
      "IR Resolution: 256 × 192 (49,152 pixels)",
      "SuperIR Resolution: Yes, on captured images & live view",
      "Temperature Range: -20°C to 400°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 50° × 37.2°",
      "NETD: < 40 mK",
      "Focus: Fixed & minimum distance 0.3m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "256 × 192",
      superIRResolution: "Yes, on captured images & live view",
      temperatureRange: "-20°C to 400°C",
      accuracy: "±2°C, ±2%",
      fov: "50° × 37.2°",
      netd: "< 40 mK",
      focus: "Fixed & minimum distance 0.3m",
      imageFrequency: "25 Hz"
    },
    image: "/thermal-measurement/TC-S240.png",
    battery: "4 hours (Built-in)",
    memory: "Built-in 16 GB",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen with auto-rotation",
    category: "compact",
    series: "Compact Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 256 × 192 (49,152 pixels)",
        "SuperIR Resolution: Yes, on captured images & live view",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 50° × 37.2°",
        "Spatial Resolution (IFOV): 3.43 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed & minimum distance 0.3m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending"
      ],
      measurement: [
        "Temperature Range: -20°C to 400°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis",
        "Color Palettes: Iron, Rainbow, Grey, Hot Metal, Rainbow HC"
      ],
      storage: [
        "Storage Medium: Built-in 16 GB",
        "Image Storage Capacity: up to 60,000 images",
        "Video Storage Capacity: up to 54 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen with auto-rotation",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, WiFi, Bluetooth"
      ]
    }
  },
  // Professional Series
  {
    id: "tc-3151",
    name: "TC 3151",
    description: "Professional thermal imager with micro SD storage",
    features: [
      "IR Resolution: 192 × 144 (27,648 pixels)",
      "SuperIR Resolution: 384 × 288",
      "Temperature Range: -20°C to 550°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 37.2° × 27.8°",
      "NETD: < 40 mK",
      "Focus: Fixed & minimum distance 0.5m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "192 × 144",
      superIRResolution: "384 × 288",
      temperatureRange: "-20°C to 550°C",
      accuracy: "±2°C, ±2%",
      fov: "37.2° × 27.8°",
      netd: "< 40 mK",
      focus: "Fixed & minimum distance 0.5m",
      imageFrequency: "25 Hz",
      spatialResolution: "3.33 mrad",
      digitalZoom: "1.0x to 8.0 x continuous"
    },
    image: "/thermal-measurement/TC-3150.png",
    battery: "6 hours (Interchangeable Battery)",
    memory: "32 GB Micro SD card",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen",
    category: "professional",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 192 × 144 (27,648 pixels)",
        "SuperIR Resolution: 384 × 288",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 37.2° × 27.8°",
        "Spatial Resolution (IFOV): 3.33 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Fixed & minimum distance 0.5m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0 x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 550°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 32 GB Micro SD card",
        "Image Storage Capacity: up to 60,000 images",
        "Video Storage Capacity: up to 150 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 6 hours operation (Interchangeable Battery)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, Laser"
      ]
    }
  },
  {
    id: "tc-3250",
    name: "TC 3250",
    description: "Professional thermal imager with manual focus",
    features: [
      "IR Resolution: 256 × 192 (49,152 pixels)",
      "SuperIR Resolution: 512 × 384",
      "Temperature Range: -20°C to 550°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 25° × 18.8°",
      "NETD: < 40 mK",
      "Focus: Manual & minimum distance 0.1m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "256 × 192",
      superIRResolution: "512 × 384",
      temperatureRange: "-20°C to 550°C",
      accuracy: "±2°C, ±2%",
      fov: "25° × 18.8°",
      netd: "< 40 mK",
      focus: "Manual & minimum distance 0.1m",
      imageFrequency: "25 Hz",
      spatialResolution: "1.74 mrad",
      digitalZoom: "1.0x to 8.0 x continuous"
    },
    image: "/thermal-measurement/TC-3250.png",
    battery: "6 hours (Interchangeable Battery)",
    memory: "32 GB Micro SD card",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen",
    category: "professional",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 256 × 192 (49,152 pixels)",
        "SuperIR Resolution: 512 × 384",
        "NETD (Thermal Sensitivity): < 40 mK",
        "Field of View: 25° × 18.8°",
        "Spatial Resolution (IFOV): 1.74 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Manual & minimum distance 0.1m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0 x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 550°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 32 GB Micro SD card",
        "Image Storage Capacity: up to 60,000 images",
        "Video Storage Capacity: up to 150 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 6 hours operation (Interchangeable Battery)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, Laser"
      ]
    }
  },
  {
    id: "tc-3360",
    name: "TC 3360",
    description: "Versatile thermal imager with wide field of view",
    features: [
      "IR Resolution: 384 × 288 (110,592 pixels)",
      "SuperIR Resolution: 768 × 576",
      "Temperature Range: -20°C to 650°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 41.1° × 30.5°",
      "NETD: < 30 mK",
      "Focus: Manual & minimum distance 0.1m",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "384 × 288",
      superIRResolution: "768 × 576",
      temperatureRange: "-20°C to 650°C",
      accuracy: "±2°C, ±2%",
      fov: "41.1° × 30.5°",
      netd: "< 30 mK",
      focus: "Manual & minimum distance 0.1m",
      imageFrequency: "30 Hz",
      spatialResolution: "1.87 mrad",
      digitalZoom: "1.0x to 8.0 x continuous"
    },
    image: "/thermal-measurement/TC-3250.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "64 GB Micro SD card (100,000 Images & 300 hours Video)",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen",
    category: "imager",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 384 × 288 (110,592 pixels)",
        "SuperIR Resolution: 768 × 576",
        "NETD (Thermal Sensitivity): < 30 mK",
        "Field of View: 41.1° × 30.5°",
        "Spatial Resolution (IFOV): 1.87 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Manual & minimum distance 0.1m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0 x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 64 GB Micro SD card",
        "Image Storage Capacity: up to 100,000 images",
        "Video Storage Capacity: up to 300 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable Battery)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, Laser"
      ]
    }
  },
  {
    id: "tc-p360",
    name: "TC P360",
    description: "Ergonomic pistol-grip thermal imager for easy operation",
    features: [
      "IR Resolution: 384 × 288 (110,592 pixels)",
      "SuperIR Resolution: 768 × 576",
      "Temperature Range: -20°C to 650°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 24° × 18°",
      "NETD: ≤ 50 mK",
      "Focus: Auto & minimum distance 0.15m",
      "Image Frequency: 25 Hz"
    ],
    specifications: {
      irResolution: "384 × 288",
      superIRResolution: "768 × 576",
      temperatureRange: "-20°C to 650°C",
      accuracy: "±2°C, ±2%",
      fov: "24° × 18° (Optional lens available)",
      netd: "≤ 50 mK",
      focus: "Auto & minimum distance 0.15m",
      imageFrequency: "25 Hz",
      spatialResolution: "1.1 mrad"
    },
    image: "/thermal-measurement/tc-p360.png",
    battery: "8 Hours (Built-in)",
    memory: "16GB TF card",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" Capacitive touch LCD screen",
    category: "pistol-grip",
    series: "Portable Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 384 × 288 (110,592 pixels)",
        "SuperIR Resolution: 768 × 576",
        "NETD (Thermal Sensitivity): ≤ 50 mK",
        "Field of View: 24° × 18° (Optional lens available)",
        "Spatial Resolution (IFOV): 1.1 mrad",
        "Image Frequency: 25 Hz",
        "Focus: Auto & minimum distance 0.15m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 10.0 x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis",
        "Color Palettes: Iron, Rainbow, Hot Metal, HCCD/Duo Vision, Duo Vision Plus"
      ],
      storage: [
        "Storage Medium: 16GB TF card",
        "Image Storage Capacity: up to 65,000+ images",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" Capacitive touch LCD screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 8 Hours operation (Built-in)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54",
        "Interfaces: USB, WiFi, Bluetooth, Voice/Note Annotation"
      ]
    }
  },
  {
    id: "tc-4360",
    name: "TC 4360",
    description: "High-resolution thermal imager for professional applications",
    features: [
      "IR Resolution: 384 × 288 (110,592 pixels)",
      "SuperIR Resolution: 768 x 576",
      "Temperature Range: -20°C to 650°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 25° × 19°",
      "NETD: < 35 mK",
      "Focus: Manual & minimum distance 0.1m",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "384 × 288",
      superIRResolution: "768 × 576",
      temperatureRange: "-20°C to 650°C",
      accuracy: "±2°C, ±2%",
      fov: "25° × 19°",
      netd: "< 35 mK",
      focus: "Manual & minimum distance 0.1m",
      imageFrequency: "30 Hz",
      spatialResolution: "1.13 mrad",
      digitalZoom: "1.0x to 8.0 x continuous"
    },
    image: "/thermal-measurement/TC-4360.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "64 GB Micro SD card (100,000 images & 300 hours Video)",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen",
    category: "imager",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 384 × 288 (110,592 pixels)",
        "SuperIR Resolution: 768 x 576",
        "NETD (Thermal Sensitivity): < 35 mK",
        "Field of View: 25° × 19° (Optional lens available)",
        "Spatial Resolution (IFOV): 1.13 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Manual & minimum distance 0.1m",
        "Image Modes: Infrared/Visual/Fusion/PIP/Blending"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted"
      ],
      storage: [
        "Storage Medium: 64 GB Micro SD card",
        "Image Storage Capacity: up to 100,000 images",
        "Video Storage Capacity: up to 300 hours",
        "File Format: Standard JPEG with measurement data"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  },
  {
    id: "tc-4460",
    name: "TC 4460 / TC 4460H",
    description: "Medium-resolution thermal imager with extended temperature range",
    features: [
      "IR Resolution: 480 × 360 (172,800 pixels)",
      "SuperIR Resolution: 960 × 720",
      "Temperature Range: -20°C to 650°C / -20°C to 2000°C (TC 4460H)",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 18.7° × 14°",
      "NETD: < 35 mK",
      "Focus: Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
      "Minimum Focus Distance: 0.25m"
    ],
    specifications: {
      irResolution: "480 × 360",
      superIRResolution: "960 × 720",
      temperatureRange: "-20°C to 650°C / -20°C to 2000°C (TC 4460H)",
      accuracy: "±2°C, ±2%",
      fov: "18.7° × 14° (Optional lens available)",
      netd: "< 35 mK",
      focus: "Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
      minFocusDistance: "0.25m",
      imageFrequency: "50 Hz",
      spatialResolution: "0.68 mrad"
    },
    image: "/thermal-measurement/TC-4460.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "64 GB Micro SD card",
    imageMode: "Infrared/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "800 × 480 Resolution, 4.3\" LCD touch screen",
    category: "imager",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 480 × 360 (172,800 pixels)",
        "SuperIR Resolution: 960 × 720",
        "NETD (Thermal Sensitivity): < 35 mK",
        "Field of View: 18.7° × 14° (Optional lens available)",
        "Spatial Resolution (IFOV): 0.68 mrad",
        "Image Frequency: 50 Hz",
        "Focus: Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
        "Minimum Focus Distance: 0.25m",
        "Image Modes: Infrared/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C / -20°C to 2000°C (TC 4460H)",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 64 GB Micro SD card",
        "Image Storage Capacity: up to 60,000 images",
        "Video Storage Capacity: up to 54 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video",
        "Annotations: Voice/Text note"
      ],
      general: [
        "Display: 800 × 480 Resolution, 4.3\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  },
  {
    id: "tc-3660",
    name: "TC 3660",
    description: "Advanced thermal imager with high-resolution sensor",
    features: [
      "IR Resolution: 640 × 480 (307,200 pixels)",
      "SuperIR Resolution: 1280 × 960",
      "Temperature Range: -20°C to 650°C",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 41.9° × 33.3°",
      "NETD: < 35 mK",
      "Focus: Manual & minimum distance 0.3m",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "640 × 480",
      superIRResolution: "1280 × 960",
      temperatureRange: "-20°C to 650°C",
      accuracy: "±2°C, ±2%",
      fov: "41.9° × 33.3°",
      netd: "< 35 mK",
      focus: "Manual & minimum distance 0.3m",
      imageFrequency: "30 Hz",
      spatialResolution: "1.13 mrad",
      digitalZoom: "1.0x to 8.0 x continuous"
    },
    image: "/thermal-measurement/TC-3650.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "64 GB Micro SD card (100,000 Images & 300 hours Video)",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "640 × 480 Resolution, 3.5\" LCD touch screen",
    category: "imager",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 640 × 480 (307,200 pixels)",
        "SuperIR Resolution: 1280 × 960",
        "NETD (Thermal Sensitivity): < 35 mK",
        "Field of View: 41.9° × 33.3°",
        "Spatial Resolution (IFOV): 1.13 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Manual & minimum distance 0.3m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0 x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 64 GB Micro SD card",
        "Image Storage Capacity: up to 100,000 images",
        "Video Storage Capacity: up to 300 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video"
      ],
      general: [
        "Display: 640 × 480 Resolution, 3.5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, Voice/Note Annotation"
      ]
    }
  },
  {
    id: "tc-4660",
    name: "TC 4660 / TC 4660H",
    description: "High-end thermal imager with high resolution and sensitivity",
    features: [
      "IR Resolution: 640 × 480 (307,200 pixels)",
      "SuperIR Resolution: 1280 × 960",
      "Temperature Range: -20°C to 650°C / -20°C to 2000°C (TC 4660H)",
      "Accuracy: ±2°C, ±2%",
      "Field of View (FOV): 25° × 19°",
      "NETD: < 35 mK",
      "Focus: Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
      "Minimum Focus Distance: 0.25m"
    ],
    specifications: {
      irResolution: "640 × 480",
      superIRResolution: "1280 × 960",
      temperatureRange: "-20°C to 650°C / -20°C to 2000°C (TC 4660H)",
      accuracy: "±2°C, ±2%",
      fov: "25° × 19°",
      netd: "< 35 mK",
      focus: "Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
      minFocusDistance: "0.25m",
      imageFrequency: "50 Hz",
      spatialResolution: "0.68 mrad"
    },
    image: "/thermal-measurement/TC-4660.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "64 GB Micro SD card",
    imageMode: "Infrared/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "800 × 480 Resolution, 4.3\" LCD touch screen",
    category: "imager",
    series: "Professional Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 640 × 480 (307,200 pixels)",
        "SuperIR Resolution: 1280 × 960",
        "NETD (Thermal Sensitivity): < 35 mK",
        "Field of View: 25° × 19°",
        "Spatial Resolution (IFOV): 0.68 mrad",
        "Image Frequency: 50 Hz",
        "Focus: Laser Assisted AF/Continuous AF/AF/Manual Focus/Touch AF",
        "Minimum Focus Distance: 0.25m",
        "Image Modes: Infrared/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 8.0x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C / -20°C to 2000°C (TC 4660H)",
        "Accuracy: ±2°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 64 GB Micro SD card",
        "Image Storage Capacity: up to 60,000 images",
        "Video Storage Capacity: up to 54 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video",
        "Annotations: Voice/Text note"
      ],
      general: [
        "Display: 800 × 480 Resolution, 4.3\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  },

  // Thermal Camcorders - highest end products
  {
    id: "tcc-7460",
    name: "TCC 7460 / TCC 742K",
    description: "Professional thermal camcorder with multiple lens options",
    features: [
      "IR Resolution: 480 × 360 (172,800 pixels)",
      "SuperIR Resolution: 960 × 720",
      "Temperature Range: -20°C to 650°C & -40°C to 2200°C (TCC 742K)",
      "Accuracy: ±1°C, ±1%",
      "Multiple Field of View (FOV): L6, L9, L19, L37",
      "NETD: < 30 mK",
      "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "480 × 360",
      superIRResolution: "960 × 720",
      temperatureRange: "-20°C to 650°C & -40°C to 2200°C (TCC 742K)",
      accuracy: "±1°C, ±1%",
      fov: "L6: 6° × 4.5°, L9: 9° × 6.8°, L19: 18.7° × 14°, L37: 37.3° × 27.8°",
      netd: "< 30 mK",
      focus: "Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      minFocusDistance: "L6: 2m; L9: 1m; L19: 0.15m; L37: 0.15m",
      imageFrequency: "30 Hz",
      spatialResolution: [
        "L6: 0.22 mrad",
        "L9: 0.33 mrad",
        "L19: 0.66 mrad",
        "L37: 1.35 mrad"
      ]
    },
     image: "/thermal-measurement/TC-4460H.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "128 GB SD card",
    imageMode: "Infrared/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "1280 × 720 Resolution, 5\" LCD touch screen",
    category: "camcorder",
    series: "Professional Camcorder Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 480 × 360 (172,800 pixels)",
        "SuperIR Resolution: 960 × 720",
        "NETD (Thermal Sensitivity): < 30 mK",
        "Multiple Field of View (FOV): L6, L9, L19, L37",
        "Spatial Resolution (IFOV): L6: 0.22 mrad; L9: 0.33 mrad; L19: 0.66 mrad; L37: 1.35 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
        "Minimum Focus Distance: L6: 2m; L9: 1m; L19: 0.15m; L37: 0.15m",
        "Image Modes: Infrared/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 12.0x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C & -40°C to 2200°C (TCC 742K)",
        "Accuracy: ±1°C, ±1%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 128 GB SD card",
        "Image Storage Capacity: up to 120,000 images",
        "Video Storage Capacity: up to 500 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video",
        "Annotations: Voice/Text note"
      ],
      general: [
        "Display: 1280 × 720 Resolution, 5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  },
  {
    id: "tcc-7660",
    name: "TCC 7660 / TCC 762K",
    description: "High-resolution thermal camcorder with multiple lens options",
    features: [
      "IR Resolution: 640 × 480 (307,200 pixels)",
      "SuperIR Resolution: 1280 × 960",
      "Temperature Range: -20°C to 650°C & -40°C to 2200°C (TCC 762K)",
      "Accuracy: ±1°C, ±2%",
      "Multiple Field of View (FOV): L8, L12, L25, L50",
      "NETD: < 30 mK",
      "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "640 × 480",
      superIRResolution: "1280 × 960",
      temperatureRange: "-20°C to 650°C & -40°C to 2200°C (TCC 762K)",
      accuracy: "±1°C, ±2%",
      fov: "L8: 8° × 6°, L12: 12° × 9°, L25: 24.8° × 18.7°, L50: 50° × 37.3°",
      netd: "< 30 mK",
      focus: "Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      minFocusDistance: "L8: 2m; L12: 1m; L25: 0.15m; L50: 0.15m",
      imageFrequency: "30 Hz",
      spatialResolution: [
        "L8: 0.22 mrad",
        "L12: 0.33 mrad",
        "L25: 0.66 mrad",
        "L50: 1.35 mrad"
      ]
    },
    image: "/thermal-measurement/tcc-7660.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "128 GB SD card",
    imageMode: "Thermal/Visual/Fusion/PIP/Blending",
    visualCamera: "3264 × 2448 (8 MP)",
    display: "1280 × 720 Resolution, 5\" LCD touch screen",
    category: "camcorder",
    series: "Professional Camcorder Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 640 × 480 (307,200 pixels)",
        "SuperIR Resolution: 1280 × 960",
        "NETD (Thermal Sensitivity): < 30 mK",
        "Multiple Field of View (FOV): L8, L12, L25, L50",
        "Spatial Resolution (IFOV): L8: 0.22 mrad; L12: 0.33 mrad; L25: 0.66 mrad; L50: 1.35 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
        "Minimum Focus Distance: L8: 2m; L12: 1m; L25: 0.15m; L50: 0.15m",
        "Image Modes: Thermal/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 12.0x continuous"
      ],
      measurement: [
        "Temperature Range: -20°C to 650°C & -40°C to 2200°C (TCC 762K)",
        "Accuracy: ±1°C, ±2%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile, Isotherm",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal"
      ],
      storage: [
        "Storage Medium: 128 GB SD card",
        "Image Storage Capacity: up to 120,000 images",
        "Video Storage Capacity: up to 500 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video",
        "Annotations: Voice/Text note"
      ],
      general: [
        "Display: 1280 × 720 Resolution, 5\" LCD touch screen",
        "Visual Camera: 3264 × 2448 (8 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  },
  {
    id: "tcc-812k",
    name: "TCC 812K",
    description: "Ultra-high resolution thermal camcorder with OLED display",
    features: [
      "IR Resolution: 1280 × 1024 (1,310,720 pixels)",
      "SuperIR Resolution: 2560 × 2048",
      "Temperature Range: -40°C to 2200°C",
      "Accuracy: ±1°C, ±1%",
      "Multiple Field of View (FOV): L8, L12, L25, L50",
      "NETD: < 20 mK",
      "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      "Image Frequency: 30 Hz"
    ],
    specifications: {
      irResolution: "1280 × 1024",
      superIRResolution: "2560 × 2048",
      temperatureRange: "-40°C to 2200°C",
      accuracy: "±1°C, ±1%",
      fov: "L8: 7.9° × 6.3°, L12: 12° × 9.6°, L25: 25° × 20°, L50: 50° × 40°",
      netd: "< 20 mK",
      focus: "Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
      minFocusDistance: "L8: 5m; L12: 1.65m; L25: 0.3m; L50: 0.2m",
      imageFrequency: "30 Hz",
      spatialResolution: [
        "L8: 0.11 mrad",
        "L12: 0.17 mrad",
        "L25: 0.34 mrad",
        "L50: 0.68 mrad"
      ]
    },
     image: "/thermal-measurement/tcc-812k.png",
    battery: "4 hours (Interchangeable Battery)",
    memory: "128GB SD card",
    imageMode: "Infrared/Visual/Fusion/PIP/Blending",
    visualCamera: "4208 × 3120 (13 MP)",
    display: "1920 × 1080 Resolution, 5.1\" OLED touch screen, Lv 360 Cd/m^2",
    category: "premium-camcorder",
    series: "Premium Camcorder Series",
    detailedFeatures: {
      imaging: [
        "IR Resolution: 1280 × 1024 (1,310,720 pixels)",
        "SuperIR Resolution: 2560 × 2048",
        "NETD (Thermal Sensitivity): < 20 mK",
        "Multiple Field of View (FOV): L8, L12, L25, L50",
        "Spatial Resolution (IFOV): L8: 0.11 mrad; L12: 0.17 mrad; L25: 0.34 mrad; L50: 0.68 mrad",
        "Image Frequency: 30 Hz",
        "Focus: Laser Assisted AF, Continuous AF, AF, Manual, Touch AF",
        "Minimum Focus Distance: L8: 5m; L12: 1.65m; L25: 0.3m; L50: 0.2m",
        "Image Modes: Infrared/Visual/Fusion/PIP/Blending",
        "Digital Zoom: 1.0x to 12.0x continuous"
      ],
      measurement: [
        "Temperature Range: -40°C to 2200°C",
        "Accuracy: ±1°C, ±1%",
        "Measurement Tools: Center Spot, Hot/Cold Auto Detection, Area Analysis, Line Profile, Isotherm, Delta T",
        "Color Palettes: Iron, Rainbow, Rainbow HC, Grey, Grey Inverted, Hot Metal, Custom Palettes"
      ],
      storage: [
        "Storage Medium: 128GB SD card",
        "Image Storage Capacity: up to 30,000 images",
        "Video Storage Capacity: up to 130 hours",
        "File Format: Standard JPEG with measurement data, MP4 for video",
        "Annotations: Voice/Text note"
      ],
      general: [
        "Display: 1920 × 1080 Resolution, 5.1\" OLED touch screen, Lv 360 Cd/m^2",
        "Visual Camera: 4208 × 3120 (13 MP)",
        "Battery: Li-ion, 4 hours operation (Interchangeable)",
        "Operating Temperature: -15°C to 50°C",
        "Storage Temperature: -40°C to 70°C",
        "Encapsulation: IP54, 2m drop test",
        "Interfaces: USB, WiFi, Bluetooth, GPS & Compass, HDMI"
      ]
    }
  }
];

// Group products by series
const productBySeries = {
  "Compact Series": thermalImagers.filter(item => item.series === "Compact Series"),
  "Pocket Series": thermalImagers.filter(item => item.series === "Pocket Series"),
  "Mobile Series": thermalImagers.filter(item => item.series === "Mobile Series"),
  "Professional Series": thermalImagers.filter(item => item.series === "Professional Series"),
  "Portable Series": thermalImagers.filter(item => item.series === "Portable Series"),
  "Professional Camcorder Series": thermalImagers.filter(item => item.series === "Professional Camcorder Series"),
  "Premium Camcorder Series": thermalImagers.filter(item => item.series === "Premium Camcorder Series")
};

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-16 md:py-24 overflow-hidden">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-400 rounded-bl-[100px] transform -skew-x-12 opacity-20"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-20"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-gray-900">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
              THERMAL <span className="text-yellow-400">IMAGERS</span>
            </h1>

            <p className="text-xl text-gray-900 leading-relaxed font-medium">
              Professional-grade thermal imaging solutions for accurate temperature measurement and visualization across various applications.
            </p>

            <div className="pt-4 flex flex-wrap gap-4">
              <Button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-yellow-50 flex items-center space-x-2"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-amber-200 to-amber-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/Untitled_design__1_-removebg-preview.png"
                alt="KRYKARD Thermal Imager"
                className="max-h-[400px] w-auto object-contain drop-shadow-2xl"
                style={{ maxWidth: "100%" }}
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  product,
  onViewDetailsClick
}) => {

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}        <div className={`relative p-4 md:p-8 flex justify-center items-center bg-${product.category === 'professional' ? 'amber-100' : 'amber-50'} h-64 md:h-80 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={product.image}
            alt={product.name}
            className="h-56 md:h-72 object-contain z-10 drop-shadow-xl"
            animate={{
              y: [0, -10, 0],
              rotate: [0, 1, 0, -1, 0],
              scale: [1, 1.02, 1]
            }}
            transition={{
              repeat: Infinity,
              duration: 5,
              ease: "easeInOut"
            }}
            whileHover={{
              scale: 1.1,
              rotate: [-2, 2, -2],
              transition: { repeat: Infinity, duration: 1 }
            }}
          />
          <div className={`absolute top-4 left-4 bg-amber-600 text-white text-xs font-bold py-1 px-3 rounded-full`}>
            {product.series}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-6 space-y-4">
          <h3 className="text-xl font-bold text-gray-900 group-hover:text-amber-600 transition-colors duration-300">
            {product.name}
          </h3>

          <p className="text-gray-700 text-sm">
            {product.description}
          </p>

          {/* Key Features */}
          <div className="space-y-2">
            {product.features.slice(0, 2).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-amber-500 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-800 text-sm font-medium">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-2 pt-2">
            <span className="inline-block bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">
              {product.specifications.irResolution}
            </span>
            <span className="inline-block bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">
              {product.battery || "Mobile"}
            </span>
          </div>

          {/* More Information Button */}
          <Button
            onClick={() => onViewDetailsClick(product)}
            className={`w-full mt-4 py-3 px-4 bg-yellow-400 hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center`}
          >
            <span>More Information</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-6 h-full border-b-4 border-amber-400"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-amber-400 to-amber-300 w-12 h-12 rounded-lg flex items-center justify-center mb-4 shadow-md">
          {icon}
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-800 flex-grow">{description}</p>
      </div>
    </motion.div>
  );
};

// Product Series Section Component
const ProductSeriesSection = ({ title, products, onViewProductDetails }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const productsPerPage = 3;
  const totalPages = Math.ceil(products.length / productsPerPage);

  const handlePrev = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  const visibleProducts = products.slice(
    currentPage * productsPerPage,
    Math.min((currentPage + 1) * productsPerPage, products.length)
  );

  return (
    <section className="mb-20 relative">
      <div className="absolute -inset-40 bg-gradient-to-br from-amber-50/50 via-transparent to-amber-50/30 dark:from-amber-900/10 dark:via-transparent dark:to-amber-900/5 rounded-3xl -z-10 blur-3xl"></div>
      <div className="text-center mb-10">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-amber-500 to-amber-600 bg-clip-text text-transparent inline-block">
          {title}
        </h2>
        <div className="w-24 h-1 bg-gradient-to-r from-amber-500 to-amber-600 mx-auto mt-3 rounded-full"></div>
      </div>

      <div className="relative max-w-7xl mx-auto">
        {products.length > productsPerPage && (
          <>
            <motion.button
              onClick={handlePrev}
              className="absolute -left-6 top-1/2 -translate-y-1/2 z-20 bg-white/90 dark:bg-gray-800/90 p-4 rounded-full shadow-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all backdrop-blur-sm"
              whileHover={{ scale: 1.1, x: -5 }}
              whileTap={{ scale: 0.9 }}
              aria-label="Previous page"
            >
              <ChevronLeft className="h-6 w-6 text-amber-500" />
            </motion.button>

            <motion.button
              onClick={handleNext}
              className="absolute -right-6 top-1/2 -translate-y-1/2 z-20 bg-white/90 dark:bg-gray-800/90 p-4 rounded-full shadow-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all backdrop-blur-sm"
              whileHover={{ scale: 1.1, x: 5 }}
              whileTap={{ scale: 0.9 }}
              aria-label="Next page"
            >
              <ChevronRight className="h-6 w-6 text-amber-500" />
            </motion.button>
          </>
        )}

        <AnimatePresence mode="wait">
          <motion.div
            key={`page-${currentPage}`}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {visibleProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onViewDetailsClick={onViewProductDetails}
              />
            ))}
          </motion.div>
        </AnimatePresence>

        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`mx-1 transition-all ${
                  index === currentPage
                    ? `w-10 h-3 bg-yellow-400 rounded-full shadow-lg shadow-yellow-400/20`
                    : 'w-3 h-3 bg-gray-300 dark:bg-gray-700 rounded-full hover:bg-yellow-400/50'
                }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

// Product Details Modal Component
const ProductDetailsModal = ({ product, isOpen, onClose }) => {
  if (!isOpen || !product) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ type: "spring", duration: 0.5 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col"
      >
        {/* Modal Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{product.name}</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-6 w-6 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Modal Body */}
        <div className="flex-grow overflow-y-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">            {/* Product Image */}
            <div className="flex flex-col items-center justify-center">
              <div className="relative bg-gradient-to-br from-amber-50/50 to-amber-50/30 dark:from-amber-900/10 dark:to-amber-900/5 p-4 md:p-8 rounded-xl w-full h-64 md:h-96 flex items-center justify-center">
                <motion.img
                  src={product.image}
                  alt={product.name}
                  className="h-full max-h-56 md:max-h-full object-contain"
                  initial={{ y: 20 }}
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 1, 0, -1, 0],
                    scale: [1, 1.02, 1]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 4,
                    ease: "easeInOut"
                  }}
                />
                <div className="absolute bottom-2 md:bottom-4 right-2 md:right-4 bg-amber-600/80 backdrop-blur-md px-2 md:px-3 py-1 rounded-full text-xs text-white font-medium">
                  {product.category === 'camcorder' && 'THERMAL CAMCORDER'}
                  {product.category === 'premium-camcorder' && 'PREMIUM CAMCORDER'}
                  {product.category === 'imager' && 'THERMAL IMAGER'}
                  {product.category === 'compact' && 'COMPACT IMAGER'}
                  {product.category === 'professional' && 'PRO IMAGER'}
                  {product.category === 'mobile' && 'MOBILE IMAGER'}
                  {product.category === 'pistol-grip' && 'PISTOL-GRIP IMAGER'}
                </div>
              </div>

              <div className="mt-6 w-full">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Quick Specifications</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <Grid className="h-4 w-4 text-amber-600 mr-2 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">IR Resolution</p>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{product.specifications.irResolution}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Thermometer className="h-4 w-4 text-amber-600 mr-2 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Temperature Range</p>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{product.specifications.temperatureRange}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Eye className="h-4 w-4 text-amber-600 mr-2 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Thermal Sensitivity</p>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{product.specifications.netd}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Battery className="h-4 w-4 text-amber-600 mr-2 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Battery Life</p>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{product.battery}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>


            {/* Product Details */}
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">{product.description}</p>

              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Key Features</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start text-gray-700 dark:text-gray-300">
                      <Check className="h-5 w-5 text-amber-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {product.detailedFeatures && (
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">Additional Information</h3>

                  <div className="bg-amber-50/50 dark:bg-amber-900/10 p-4 rounded-lg mb-4">
                    <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2">Image Modes</h4>
                    <p className="text-gray-700 dark:text-gray-300">{product.imageMode}</p>
                  </div>

                  {product.visualCamera && (
                    <div className="bg-amber-50/50 dark:bg-amber-900/10 p-4 rounded-lg mb-4">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2">Visual Camera</h4>
                      <p className="text-gray-700 dark:text-gray-300">{product.visualCamera}</p>
                    </div>
                  )}

                  {product.display && (
                    <div className="bg-amber-50/50 dark:bg-amber-900/10 p-4 rounded-lg mb-4">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2">Display</h4>
                      <p className="text-gray-700 dark:text-gray-300">{product.display}</p>
                    </div>
                  )}

                  {product.memory && (
                    <div className="bg-amber-50/50 dark:bg-amber-900/10 p-4 rounded-lg mb-4">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2">Storage</h4>
                      <p className="text-gray-700 dark:text-gray-300">{product.memory}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex flex-wrap gap-4 bg-gradient-to-r from-amber-50/50 to-amber-100/50 dark:from-amber-900/20 dark:to-amber-800/20">
          <Button
            onClick={() => onClose()}
            className="inline-flex items-center justify-center bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold px-4 py-2 rounded-lg shadow-md transition-all duration-300"
          >
            <FileText className="mr-2 h-4 w-4" />
            View Brochure
          </Button>

          <a
            href={`/enquiry?product=${product.id}`}
            className="inline-flex items-center justify-center bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm"
          >
            <Mail className="mr-2 h-4 w-4" />
            Request Information
          </a>
        </div>
      </motion.div>
    </div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-3 border-b border-amber-100">
    <div className="bg-amber-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-800 font-medium">{text}</span>
  </div>
);

// Combined Tab Component for Features and Measurements
const ProductTabContent = ({ activeProductType, activeTab }) => {
  // Features data based on product type
  const features = {
    compact: [
      "IR Resolution: up to 256 × 192 (49,152 pixels)",
      "Compact and lightweight design for handheld operation",
      "Temperature Range: -20°C to 550°C",
      "Thermal Sensitivity: < 40 mK",
      "Fixed Focus with minimum distance of 0.3m",
      "Built-in memory (4GB-16GB)",
      "Multiple Image Modes: Thermal/Visual/Fusion/PIP/Blending",
      "Battery life: 4-8 hours",
      "Visual camera: up to 8 MP",
      "USB, WiFi connectivity"
    ],
    pocket: [
      "IR Resolution: 96 × 96 (9,216 pixels)",
      "SuperIR Resolution: up to 240 × 240",
      "Pocket-sized design for ultimate portability",
      "Temperature Range: -20°C to 550°C",
      "Thermal Sensitivity: < 50 mK",
      "Fixed Focus with minimum distance of 0.1m",
      "Built-in memory (4GB)",
      "Extended battery life: up to 8 hours",
      "Visual camera: 0.3 MP",
      "USB connectivity"
    ],
    mobile: [
      "IR Resolution: 256 × 192 (49,152 pixels)",
      "SuperIR Resolution enhancement",
      "Smartphone attachment design",
      "Temperature Range: -20°C to 400°C",
      "Thermal Sensitivity: < 40 mK",
      "Fixed Focus with minimum distance of 0.2m",
      "Uses smartphone storage",
      "Powered by smartphone",
      "Multiple connectivity options (USB-C, Lightning)",
      "Compact and portable design"
    ],
    portable: [
      "IR Resolution: 384 × 288 (110,592 pixels)",
      "SuperIR Resolution: 768 × 576",
      "Ergonomic pistol-grip design",
      "Temperature Range: -20°C to 650°C",
      "Thermal Sensitivity: ≤ 50 mK",
      "Auto Focus with minimum distance of 0.15m",
      "16GB TF card storage",
      "Battery life: 8 hours (Built-in)",
      "Visual camera: 8 MP",
      "Voice/Note Annotation capability"
    ],
    professional: [
      "IR Resolution: up to 640 × 480 (307,200 pixels)",
      "Temperature Range: -20°C to 2000°C (high-temp models)",
      "Thermal Sensitivity: < 30 mK",
      "Manual or Auto focus options",
      "Digital Zoom: 1.0x to 8.0x continuous",
      "Interchangeable battery design",
      "Advanced measurement tools",
      "SD card storage (up to 128GB)",
      "Optional lens configurations",
      "IP54 protection, 2m drop test certified"
    ],
    camcorder: [
      "IR Resolution: up to 1280 × 1024 (1.3MP)",
      "Ultra-high thermal sensitivity: down to < 20 mK",
      "Multiple lens options for different field of view",
      "Temperature Range: -40°C to 2200°C",
      "Advanced focusing systems including Laser Assisted AF",
      "High-resolution displays up to 1920 × 1080",
      "Comprehensive measurement tools and analysis features",
      "Extensive storage capacity (up to 500 hours video)",
      "Advanced connectivity including HDMI, GPS, Bluetooth",
      "Professional-grade ergonomics and controls"
    ],
    "premium-camcorder": [
      "IR Resolution: 1280 × 1024 (1,310,720 pixels)",
      "SuperIR Resolution: 2560 × 2048",
      "Ultra-high thermal sensitivity: < 20 mK",
      "Temperature Range: -40°C to 2200°C",
      "Multiple lens options (L8, L12, L25, L50)",
      "OLED display with 1920 × 1080 resolution",
      "Advanced measurement tools including Delta T",
      "128GB SD card storage",
      "13 MP visual camera",
      "Professional-grade design with comprehensive interfaces"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    compact: [
      { label: "IR Resolution", value: "96 × 96 to 256 × 192 pixels" },
      { label: "Temperature Range", value: "-20°C to 550°C" },
      { label: "Thermal Sensitivity", value: "< 40-50 mK" },
      { label: "Field of View", value: "37.2° × 50.0° to 50° × 50°" },
      { label: "Accuracy", value: "±2°C, ±2%" },
      { label: "Image Frequency", value: "25 Hz" }
    ],
    pocket: [
      { label: "IR Resolution", value: "96 × 96 pixels" },
      { label: "Temperature Range", value: "-20°C to 550°C" },
      { label: "Thermal Sensitivity", value: "< 50 mK" },
      { label: "Field of View", value: "50° × 50°" },
      { label: "Accuracy", value: "±2°C, ±2%" },
      { label: "Image Frequency", value: "25 Hz" }
    ],
    mobile: [
      { label: "IR Resolution", value: "256 × 192 pixels" },
      { label: "Temperature Range", value: "-20°C to 400°C" },
      { label: "Thermal Sensitivity", value: "< 40 mK" },
      { label: "Field of View", value: "50° × 37.2°" },
      { label: "Accuracy", value: "±2°C, ±2%" },
      { label: "Image Frequency", value: "50 Hz" }
    ],
    portable: [
      { label: "IR Resolution", value: "384 × 288 pixels" },
      { label: "Temperature Range", value: "-20°C to 650°C" },
      { label: "Thermal Sensitivity", value: "≤ 50 mK" },
      { label: "Field of View", value: "24° × 18° (Optional lens available)" },
      { label: "Accuracy", value: "±2°C, ±2%" },
      { label: "Image Frequency", value: "25 Hz" }
    ],
    professional: [
      { label: "IR Resolution", value: "192 × 144 to 640 × 480 pixels" },
      { label: "Temperature Range", value: "-20°C to 2000°C (high-temp models)" },
      { label: "Thermal Sensitivity", value: "< 30-40 mK" },
      { label: "Field of View", value: "Varies by model, 25° × 19° to 41.9° × 33.3°" },
      { label: "Accuracy", value: "±2°C, ±2%" },
      { label: "Image Frequency", value: "25-50 Hz" }
    ],
    camcorder: [
      { label: "IR Resolution", value: "480 × 360 to 640 × 480 pixels" },
      { label: "Temperature Range", value: "-20°C to 2200°C" },
      { label: "Thermal Sensitivity", value: "< 30 mK" },
      { label: "Field of View", value: "Multiple lens options" },
      { label: "Accuracy", value: "±1°C, ±2%" },
      { label: "Image Frequency", value: "30 Hz" }
    ],
    "premium-camcorder": [
      { label: "IR Resolution", value: "1280 × 1024 pixels" },
      { label: "Temperature Range", value: "-40°C to 2200°C" },
      { label: "Thermal Sensitivity", value: "< 20 mK" },
      { label: "Field of View", value: "Multiple lens options (L8, L12, L25, L50)" },
      { label: "Accuracy", value: "±1°C, ±1%" },
      { label: "Image Frequency", value: "30 Hz" }
    ]
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-amber-500 to-amber-400 p-4">
          <h3 className="text-2xl font-bold text-center text-white">Salient Features</h3>
        </div>
        <div className="p-6">
          <div className="space-y-2">
            {features[activeProductType].map((feature, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-3 hover:bg-amber-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 text-amber-600 flex items-center justify-center mr-3">
                  <Check className="h-4 w-4" />
                </div>
                <span className="text-gray-800 font-medium">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-amber-500 to-amber-400 p-4">
          <h3 className="text-2xl font-bold text-center text-white">Measurements</h3>
        </div>
        <div className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            {measurements[activeProductType].map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 border border-amber-100"
              >
                <h4 className="font-semibold text-gray-900 mb-2 border-b border-amber-200 pb-2">{item.label}</h4>
                <div className="text-gray-800">
                  {item.value.split('\n').map((line, i) => (
                    <div key={i} className="mb-1">{line}</div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Contact Section Component with improved styling
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-yellow-400 py-8 px-4 rounded-2xl shadow-lg">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Need More Information?</h2>
        <p className="text-gray-900 mb-8 max-w-3xl mx-auto font-medium">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about the KRYKARD Thermal Imagers.
        </p>
        <Button
          className="inline-flex items-center px-8 py-4 bg-white hover:bg-gray-50 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 border-2 border-gray-900"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );
};

// Comparison Table Component with improved styling
const ComparisonTable = ({ productType }) => {
  const models = {
    compact: ['TC S030', 'TC 2150', 'TC S240'],
    pocket: ['TC S030', 'TC E050'],
    mobile: ['MA 250'],
    portable: ['TC P360'],
    professional: ['TC 3360', 'TC 4460', 'TC 4660'],
    camcorder: ['TCC 7460', 'TCC 7660'],
    "premium-camcorder": ['TCC 812K']
  };

  const features = {
    compact: [
      { name: 'IR Resolution', values: ['96 × 96', '192 × 144', '256 × 192'] },
      { name: 'SuperIR Resolution', values: ['Yes', 'Yes', 'Yes'] },
      { name: 'Temperature Range', values: ['-20°C to 350°C', '-20°C to 550°C', '-20°C to 400°C'] },
      { name: 'Display', values: ['3.5" LCD Touch', '3.2" LCD', '3.5" LCD Touch Auto-rotation'] },
      { name: 'Battery Life', values: ['4 hours', '6 hours', '4 hours'] },
      { name: 'Visual Camera', values: ['0.3 MP', '2 MP', '8 MP'] }
    ],
    pocket: [
      { name: 'IR Resolution', values: ['96 × 96', '96 × 96'] },
      { name: 'SuperIR Resolution', values: ['Yes', '240 × 240'] },
      { name: 'Temperature Range', values: ['-20°C to 350°C', '-20°C to 550°C'] },
      { name: 'Display', values: ['3.5" LCD Touch', '2.4" LCD'] },
      { name: 'Battery Life', values: ['4 hours', '8 hours'] },
      { name: 'Visual Camera', values: ['0.3 MP', '0.3 MP'] }
    ],
    mobile: [
      { name: 'IR Resolution', values: ['256 × 192'] },
      { name: 'SuperIR Resolution', values: ['Yes'] },
      { name: 'Temperature Range', values: ['-20°C to 400°C'] },
      { name: 'Connectivity', values: ['USB-C/Lightning'] },
      { name: 'Power Source', values: ['Smartphone'] },
      { name: 'Image Modes', values: ['Thermal/PIP'] }
    ],
    portable: [
      { name: 'IR Resolution', values: ['384 × 288'] },
      { name: 'SuperIR Resolution', values: ['768 × 576'] },
      { name: 'Temperature Range', values: ['-20°C to 650°C'] },
      { name: 'Focus', values: ['Auto'] },
      { name: 'Display', values: ['3.5" Touch LCD'] },
      { name: 'Battery Life', values: ['8 Hours'] }
    ],
    professional: [
      { name: 'IR Resolution', values: ['384 × 288', '480 × 360', '640 × 480'] },
      { name: 'SuperIR Resolution', values: ['768 × 576', '960 × 720', '1280 × 960'] },
      { name: 'Temperature Range', values: ['-20°C to 650°C', '-20°C to 650°C/2000°C', '-20°C to 650°C/2000°C'] },
      { name: 'Focus', values: ['Manual', 'Multi AF Options', 'Multi AF Options'] },
      { name: 'Display', values: ['3.5" LCD Touch', '4.3" LCD Touch', '4.3" LCD Touch'] },
      { name: 'Battery', values: ['4 hours', '4 hours', '4 hours'] }
    ],
    camcorder: [
      { name: 'IR Resolution', values: ['480 × 360', '640 × 480'] },
      { name: 'SuperIR Resolution', values: ['960 × 720', '1280 × 960'] },
      { name: 'Temperature Range', values: ['-20°C to 650°C/2200°C', '-20°C to 650°C/2200°C'] },
      { name: 'Thermal Sensitivity', values: ['< 30 mK', '< 30 mK'] },
      { name: 'Display', values: ['5" LCD Touch', '5" LCD Touch'] },
      { name: 'Lens Options', values: ['4 Options', '4 Options'] }
    ],
    "premium-camcorder": [
      { name: 'IR Resolution', values: ['1280 × 1024'] },
      { name: 'SuperIR Resolution', values: ['2560 × 2048'] },
      { name: 'Temperature Range', values: ['-40°C to 2200°C'] },
      { name: 'Thermal Sensitivity', values: ['< 20 mK'] },
      { name: 'Display', values: ['5.1" OLED Touch'] },
      { name: 'Lens Options', values: ['4 Options'] }
    ]
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-8">
      <div className="bg-gradient-to-r from-amber-500 to-amber-400 p-4">
        <h3 className="text-2xl font-bold text-center text-white">Model Comparison</h3>
      </div>
      <div className="p-4 md:p-6 overflow-x-auto -mx-4 md:mx-0">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6 py-3 bg-amber-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Feature</th>
              {models[productType].map((model, idx) => (
                <th key={idx} className={`px-6 py-3 bg-amber-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${idx === models[productType].length - 1 ? 'rounded-tr-lg' : ''}`}>
                  {model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {features[productType].map((feature, idx) => (
              <motion.tr
                key={idx}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className={idx % 2 === 0 ? 'bg-white hover:bg-amber-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-amber-50 transition-colors duration-200'}
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{feature.name}</td>
                {feature.values.map((value, i) => (
                  <td key={i} className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-center">
                    {value}
                  </td>
                ))}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};


// Applications Section Component with improved styling
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Building Inspections",
      icon: <Camera className="h-8 w-8 text-amber-600" />,
      description: "Detect thermal bridges, insulation issues, moisture problems, and energy loss in buildings."
    },
    {
      title: "Electrical Inspections",
      icon: <Flame className="h-8 w-8 text-amber-600" />,
      description: "Identify overheating electrical components, loose connections, and potential failure points before they cause downtime."
    },
    {
      title: "Mechanical Inspections",
      icon: <Activity className="h-8 w-8 text-amber-600" />,
      description: "Monitor mechanical components for overheating, friction issues, and proper functioning."
    },
    {
      title: "Research & Development",
      icon: <BarChart3 className="h-8 w-8 text-amber-600" />,
      description: "Analyze thermal patterns in prototypes, components, and materials during development and testing phases."
    }
  ];

  return (
    <div className="py-12 bg-gradient-to-br from-amber-50 to-white rounded-2xl my-12 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Application Areas</h2>
          <p className="text-lg text-gray-800 max-w-3xl mx-auto font-medium">
            Our thermal imagers are designed for a wide range of thermal measurement applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-amber-400"
            >
              <div className="bg-amber-100 w-16 h-16 rounded-lg flex items-center justify-center mb-4 text-amber-600">
                {app.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">{app.title}</h3>
              <p className="text-gray-800 font-medium">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main ThermalImagers Component
const ThermalImagers = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("compact");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null as any);
  const navigate = useNavigate();
  const location = useLocation();

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');
    const detailTab = params.get('detailTab');

    if (tab) setActiveTab(tab);
    if (product) setActiveProductType(product);
    if (detailTab) setActiveDetailTab(detailTab);
  }, [location]);

  // Effect to redirect from documents tab to overview tab
  useEffect(() => {
    if (activeDetailTab === "documents") {
      setActiveDetailTab("overview");
    }
  }, [activeDetailTab]);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Handler for View Product Details
  const handleViewProductDetails = (product: any) => {
    // Determine the product type based on the series
    let productType = "compact"; // default

    if (product.series === "Pocket Series") {
      productType = "pocket";
    } else if (product.series === "Mobile Series") {
      productType = "mobile";
    } else if (product.series === "Compact Series") {
      productType = "compact";
    } else if (product.series === "Professional Series") {
      productType = "professional";
    } else if (product.series === "Portable Series") {
      productType = "portable";
    } else if (product.series === "Professional Camcorder Series") {
      productType = "camcorder";
    } else if (product.series === "Premium Camcorder Series") {
      productType = "premium-camcorder";
    }

    // First set the selected product
    setSelectedProduct(product);

    // Disable smooth scrolling temporarily
    document.documentElement.style.scrollBehavior = 'auto';

    // Immediately scroll to top without animation
    window.scrollTo(0, 0);

    // Then navigate to the details tab with the selected product type
    setActiveTab('details');
    setActiveProductType(productType);
    setActiveDetailTab('overview');
    navigate(`?tab=details&product=${productType}&detailTab=overview`, { replace: true });

    // Reset scroll behavior after a short delay
    setTimeout(() => {
      document.documentElement.style.scrollBehavior = '';
    }, 100);
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Thermometer className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Camera className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Thermal Imagers"
      subtitle=""
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id                      ? "bg-yellow-500 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-600"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div                      layoutId="navIndicator"
                      className="absolute inset-0 bg-yellow-500 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}&detailTab=${activeDetailTab}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-16 bg-gradient-to-br from-amber-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-bold text-amber-600 mb-4">Why Choose Our Thermal Imagers?</h2>
                  <p className="mt-4 text-lg text-gray-800 max-w-3xl mx-auto font-medium">
                    Precision-engineered thermal imaging solutions that combine accuracy, portability, and advanced features
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <FeatureHighlight
                  icon={<Eye className="h-6 w-6 text-white" />}
                  title="Superior Sensitivity"
                  description="Our thermal imagers feature sensitivity down to <20 mK, detecting the smallest temperature differences for highly detailed thermal analysis."
                />

                <FeatureHighlight
                  icon={<Zap className="h-6 w-6 text-white" />}
                  title="SuperIR Technology"
                  description="Advanced SuperIR technology enhances resolution by up to 4x, providing more detailed thermal images for precise analysis and diagnosis."
                />

                <FeatureHighlight
                  icon={<Camera className="h-6 w-6 text-white" />}
                  title="Versatile Imaging Modes"
                  description="Multiple imaging modes including Thermal, Visual, Fusion, PIP, and Blending to meet various application requirements and enhance diagnostics."
                />
              </div>
            </div>
          </div>

          {/* Products Sections - Display all products by series */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <span className="inline-block bg-amber-100 text-amber-800 px-4 py-1 rounded-full text-sm font-semibold mb-4">
                PRODUCT LINEUP
              </span>
              <h2 className="text-4xl font-bold mb-6 text-gray-900">
                Our Complete Thermal Imager Range
              </h2>
              <p className="max-w-3xl mx-auto text-gray-800 text-lg font-medium">
                Browse our comprehensive range of thermal imaging solutions for various applications and industries
              </p>
            </motion.div>

            {/* Pocket Series */}
            {productBySeries["Pocket Series"] && productBySeries["Pocket Series"].length > 0 && (
              <ProductSeriesSection
                title="Pocket Series"
                products={productBySeries["Pocket Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Mobile Series */}
            {productBySeries["Mobile Series"] && productBySeries["Mobile Series"].length > 0 && (
              <ProductSeriesSection
                title="Mobile Series"
                products={productBySeries["Mobile Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Compact Series */}
            {productBySeries["Compact Series"] && productBySeries["Compact Series"].length > 0 && (
              <ProductSeriesSection
                title="Compact Series"
                products={productBySeries["Compact Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Professional Series */}
            {productBySeries["Professional Series"] && productBySeries["Professional Series"].length > 0 && (
              <ProductSeriesSection
                title="Professional Series"
                products={productBySeries["Professional Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Portable Series */}
            {productBySeries["Portable Series"] && productBySeries["Portable Series"].length > 0 && (
              <ProductSeriesSection
                title="Portable Series"
                products={productBySeries["Portable Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Professional Camcorder Series */}
            {productBySeries["Professional Camcorder Series"] && productBySeries["Professional Camcorder Series"].length > 0 && (
              <ProductSeriesSection
                title="Professional Camcorder Series"
                products={productBySeries["Professional Camcorder Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}

            {/* Premium Camcorder Series */}
            {productBySeries["Premium Camcorder Series"] && productBySeries["Premium Camcorder Series"].length > 0 && (
              <ProductSeriesSection
                title="Premium Camcorder Series"
                products={productBySeries["Premium Camcorder Series"]}
                onViewProductDetails={handleViewProductDetails}
              />
            )}
          </div>

          {/* Contact Information Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-yellow-50 rounded-xl shadow-md p-6 mb-8 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-2xl font-bold text-gray-800 mb-6 relative z-10 border-b border-yellow-200 pb-3">
              Select <span className="text-yellow-600">Product Category</span>
            </h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 relative z-10">
              {[
                { id: "pocket", label: "Pocket Series" },
                { id: "mobile", label: "Mobile Series" },
                { id: "compact", label: "Compact Series" },
                { id: "portable", label: "Portable Series" },
                { id: "professional", label: "Professional Series" },
                { id: "camcorder", label: "Camcorder Series" },
                { id: "premium-camcorder", label: "Premium Camcorder" }
              ].map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}
                  onClick={() => {
                    setActiveProductType(option.id);
                    setSelectedProduct(null); // Clear any selected product
                    navigate(`?tab=details&product=${option.id}&detailTab=${activeDetailTab}`, { replace: true });
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProductType === option.id                      ? "ring-2 ring-yellow-500"
                      : "hover:ring-1 hover:ring-yellow-500"
                  }`}
                >
                  <div                  className={`h-full py-5 px-4 flex flex-col items-center text-center ${
                    activeProductType === option.id
                      ? "bg-yellow-500 text-gray-900"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <h3 className={`text-xl font-bold ${activeProductType === option.id ? "text-gray-900" : "text-gray-800"}`}>
                      {option.label}
                    </h3>

                    {activeProductType === option.id && (
                      <div className="mt-3 bg-white bg-opacity-70 rounded-full px-4 py-1 text-sm font-semibold text-gray-900">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Products in Selected Category */}
            <div className="mt-8 pt-6 border-t border-yellow-200">
              <h3 className="text-xl font-bold text-gray-800 mb-5 border-b border-yellow-200 pb-2">Products in {
                activeProductType === "pocket" ? "Pocket Series" :
                activeProductType === "mobile" ? "Mobile Series" :
                activeProductType === "compact" ? "Compact Series" :
                activeProductType === "portable" ? "Portable Series" :
                activeProductType === "professional" ? "Professional Series" :
                activeProductType === "camcorder" ? "Professional Camcorder Series" :
                "Premium Camcorder Series"
              }</h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {productBySeries[
                  activeProductType === "pocket" ? "Pocket Series" :
                  activeProductType === "mobile" ? "Mobile Series" :
                  activeProductType === "compact" ? "Compact Series" :
                  activeProductType === "portable" ? "Portable Series" :
                  activeProductType === "professional" ? "Professional Series" :
                  activeProductType === "camcorder" ? "Professional Camcorder Series" :
                  "Premium Camcorder Series"
                ].map((product, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: idx * 0.05 }}
                    className={`bg-white rounded-lg p-5 hover:shadow-lg transition-all duration-300 border-2 cursor-pointer ${
                      selectedProduct && selectedProduct.id === product.id                        ? "border-yellow-500 ring-2 ring-yellow-500 shadow-lg"
                        : "border-yellow-200 hover:border-yellow-500"
                    }`}
                    onClick={() => {
                      setSelectedProduct(product);
                      // Scroll to the product details section
                      setTimeout(() => {
                        const detailsSection = document.querySelector('.product-details-section');
                        if (detailsSection) {
                          detailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }, 100);
                    }}
                  >
                    <div className="flex flex-col items-center">
                      <h4 className="text-lg font-bold text-gray-800 text-center">{product.name}</h4>
                      <p className="text-sm text-gray-600 text-center mt-2 font-medium">{product.specifications.irResolution}</p>

                      {selectedProduct && selectedProduct.id === product.id && (                        <div className="mt-3 bg-yellow-500 rounded-full px-4 py-1 text-sm font-semibold text-gray-900">
                          Selected
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Selected Product Detail Section - Only shown when a product is selected */}
          {selectedProduct && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-xl shadow-xl p-8 mb-12 product-details-section overflow-hidden relative"
            >
              {/* Background decorative elements */}
              <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-100 rounded-full opacity-20 transform translate-x-1/3 -translate-y-1/3 blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-96 h-96 bg-yellow-200 rounded-full opacity-10 transform -translate-x-1/3 translate-y-1/3 blur-3xl"></div>

              {/* Product header with badge */}
              <div className="mb-8 pb-4 border-b border-gray-100 relative z-10">
                {/* Product badges */}
                <div className="flex items-center mb-4">
                  <span className="bg-yellow-100 text-yellow-800 text-xs font-bold px-3 py-1 rounded-full mr-3">
                    {selectedProduct.series}
                  </span>                  <span className="bg-yellow-500 text-gray-900 text-xs font-bold px-3 py-1 rounded-full">
                    {selectedProduct.category === 'camcorder' && 'THERMAL CAMCORDER'}
                    {selectedProduct.category === 'premium-camcorder' && 'PREMIUM CAMCORDER'}
                    {selectedProduct.category === 'imager' && 'THERMAL IMAGER'}
                    {selectedProduct.category === 'compact' && 'COMPACT IMAGER'}
                    {selectedProduct.category === 'professional' && 'PRO IMAGER'}
                    {selectedProduct.category === 'mobile' && 'MOBILE IMAGER'}
                    {selectedProduct.category === 'pistol-grip' && 'PISTOL-GRIP IMAGER'}
                    {selectedProduct.category === 'pocket' && 'POCKET IMAGER'}
                  </span>
                  <button
                    onClick={() => setSelectedProduct(null)}
                    className="p-2 rounded-full hover:bg-yellow-50 transition-colors ml-auto"
                  >
                    <X className="h-5 w-5 text-gray-400 hover:text-yellow-600" />
                  </button>
                </div>

                {/* Title and tabs on the same line */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                  <h2 className="text-3xl font-bold text-gray-800">{selectedProduct.name}</h2>

                  <div className="flex flex-wrap gap-3 md:w-auto w-full">
                    {["Overview", "Features", "Specifications"].map((tab) => (
                      <button
                        key={tab}
                        onClick={() => setActiveDetailTab(tab.toLowerCase())}
                        className={`py-3 px-6 rounded-full font-bold text-lg transition-all duration-200 ${
                          activeDetailTab === tab.toLowerCase()                            ? "bg-yellow-500 text-gray-900 shadow-md flex items-center justify-center"
                            : "bg-gray-100 text-gray-700 hover:bg-yellow-100"
                        }`}
                      >
                        {activeDetailTab === tab.toLowerCase() && tab.toLowerCase() === "overview" && (
                          <Camera className="mr-2 h-5 w-5" />
                        )}
                        {activeDetailTab === tab.toLowerCase() && tab.toLowerCase() === "features" && (
                          <Check className="mr-2 h-5 w-5" />
                        )}
                        {activeDetailTab === tab.toLowerCase() && tab.toLowerCase() === "specifications" && (
                          <FileText className="mr-2 h-5 w-5" />
                        )}
                        {tab}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-10 relative z-10">
                {/* Left Column - Image and Quick Specs */}
                <div className="flex flex-col">
                  <div className="relative bg-yellow-50 p-4 md:p-8 rounded-xl w-full h-48 md:h-64 flex items-center justify-center shadow-inner mb-8">
                    <motion.img
                      src={selectedProduct.image}
                      alt={selectedProduct.name}
                      className="h-full object-contain"
                      initial={{ y: 20 }}
                      animate={{ y: [0, -10, 0] }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut"
                      }}
                    />
                  </div>

                  {/* Quick Specifications Cards */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-yellow-100">
                      <div className="flex items-center mb-2">
                        <Grid className="h-5 w-5 text-yellow-600 mr-2" />
                        <h4 className="font-semibold text-gray-800">Resolution</h4>
                      </div>
                      <p className="text-gray-800 font-medium">{selectedProduct.specifications.irResolution}</p>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-yellow-100">
                      <div className="flex items-center mb-2">
                        <Thermometer className="h-5 w-5 text-yellow-600 mr-2" />
                        <h4 className="font-semibold text-gray-800">Temp Range</h4>
                      </div>
                      <p className="text-gray-800 font-medium">{selectedProduct.specifications.temperatureRange}</p>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-yellow-100">
                      <div className="flex items-center mb-2">
                        <Eye className="h-5 w-5 text-yellow-600 mr-2" />
                        <h4 className="font-semibold text-gray-800">Sensitivity</h4>
                      </div>
                      <p className="text-gray-800 font-medium">{selectedProduct.specifications.netd}</p>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-yellow-100">
                      <div className="flex items-center mb-2">
                        <Battery className="h-5 w-5 text-yellow-600 mr-2" />
                        <h4 className="font-semibold text-gray-800">Battery</h4>
                      </div>
                      <p className="text-gray-800 font-medium">{selectedProduct.battery || "N/A"}</p>
                    </div>
                  </div>
                </div>

                {/* Right Column - Product Details with Tabs */}
                <div className="flex flex-col">

                  {/* Tab Content */}
                  <div className="bg-white rounded-xl border border-gray-100 p-5 min-h-[400px]">                    {/* Overview Tab */}
                    {activeDetailTab === "overview" && (
                      <div className="animate-fadeIn">
                        <h3 className="text-xl font-bold text-gray-800 mb-4">Product Overview</h3>
                        <p className="text-gray-700 leading-relaxed mb-6">{selectedProduct.description}</p>
                        <div className="bg-yellow-500 rounded-lg p-4 border border-yellow-500">
                          <h4 className="font-semibold text-gray-900 mb-2">Key Applications</h4>
                          <p className="text-gray-900">Building inspections and energy audits, electrical and mechanical inspections, research and development</p>
                        </div>
                      </div>
                    )}

                    {/* Features Tab */}
                    {activeDetailTab === "features" && (
                      <div className="animate-fadeIn">
                        <h3 className="text-xl font-bold text-gray-800 mb-4">Key Features</h3>
                        <ul className="space-y-3">
                          {selectedProduct.features.map((feature, index) => (
                            <li key={index} className="flex items-start bg-gray-50 p-3 rounded-lg border border-yellow-100">
                              <div className="bg-white p-1 rounded-full shadow-sm mr-3 mt-0.5">
                                <Check className="h-4 w-4 text-yellow-600" />
                              </div>
                              <span className="text-gray-700">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Specifications Tab */}
                    {activeDetailTab === "specifications" && (
                      <div className="animate-fadeIn">
                        <h3 className="text-xl font-bold text-gray-800 mb-4">Technical Specifications</h3>

                        <div className="grid grid-cols-1 gap-4">
                          <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                            <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">IR Resolution</div>
                            <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.specifications.irResolution}</div>
                          </div>

                          <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                            <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Temperature Range</div>
                            <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.specifications.temperatureRange}</div>
                          </div>

                          <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                            <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Thermal Sensitivity</div>
                            <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.specifications.netd}</div>
                          </div>

                          <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                            <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Battery Life</div>
                            <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.battery || "N/A"}</div>
                          </div>

                          {selectedProduct.imageMode && (
                            <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                              <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Image Modes</div>
                              <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.imageMode}</div>
                            </div>
                          )}

                          {selectedProduct.visualCamera && (
                            <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                              <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Visual Camera</div>
                              <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.visualCamera}</div>
                            </div>
                          )}

                          {selectedProduct.display && (
                            <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                              <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Display</div>
                              <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.display}</div>
                            </div>
                          )}

                          {selectedProduct.memory && (
                            <div className="flex flex-col sm:flex-row border-b border-gray-100 py-3">
                              <div className="w-full sm:w-1/3 font-semibold text-gray-700 mb-1 sm:mb-0">Storage</div>
                              <div className="w-full sm:w-2/3 text-gray-800">{selectedProduct.memory}</div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* If Documents tab is selected, we'll handle it with useEffect */}
                  </div>                  Action Buttons
                  <div className="mt-auto pt-6">
                    <div className="flex flex-wrap gap-4">
                      <Button
                        className="flex-1 inline-flex items-center justify-center bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold px-6 py-3 rounded-xl shadow-md transition-all duration-300"
                        onClick={handleViewBrochure}
                      >
                        <FileText className="mr-2 h-5 w-5" />
                        View Brochure
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}



          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >              <h1 className="text-3xl font-bold text-yellow-500 mb-4">Applications</h1>
              <p className="text-lg text-gray-800 max-w-3xl mx-auto">
                KRYKARD thermal imagers are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-yellow-500 mb-6">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-500 p-3 rounded-lg text-gray-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Building Diagnostics</h3>
                  <p className="text-gray-800 font-medium">
                    Detect insulation defects, thermal bridges, moisture issues, and energy loss in building envelopes and structures.
                  </p>
                </div>
              </motion.div>              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-500 p-3 rounded-lg text-gray-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Industrial Maintenance</h3>
                  <p className="text-gray-800 font-medium">
                    Identify electrical faults, mechanical wear, friction issues, and potential equipment failures before they occur.
                  </p>
                </div>
              </motion.div>              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-500 p-3 rounded-lg text-gray-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Renewable Energy</h3>
                  <p className="text-gray-800 font-medium">
                    Inspect solar panels for hot spots, connection issues, and overall efficiency in photovoltaic installations.
                  </p>
                </div>
              </motion.div>              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-500 p-3 rounded-lg text-gray-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V4a1 1 0 011-1h2a1 1 0 011 1v14a1 1 0 01-1 1h-2a1 1 0 01-1-1z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">HVAC Systems</h3>
                  <p className="text-gray-800 font-medium">
                    Analyze heating, ventilation, and air conditioning performance, detect leaks, and identify efficiency issues.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-8">              <Button
                className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* Product Details Modal */}
      <AnimatePresence>
        {showProductModal && selectedProduct && (
          <ProductDetailsModal
            product={selectedProduct}
            isOpen={showProductModal}
            onClose={() => setShowProductModal(false)}
          />
        )}
      </AnimatePresence>
    </PageLayout>
  );
};

export default ThermalImagers;