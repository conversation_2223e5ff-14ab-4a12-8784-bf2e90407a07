import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Check,
  ChevronRight,
  ArrowDownCircle,
  Zap,
  BarChart,
  Gauge,
  Shield,
  Database,
  LineChart,
  FileText,
  ExternalLink
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-16 md:py-24 overflow-hidden">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-gray-900">DRANETZ Power Quality</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
              POWER <span className="text-yellow-500">ANALYZERS</span>
            </h1>

            <p className="text-xl text-gray-900 leading-relaxed font-medium">
              Professional-grade instruments for comprehensive power quality analysis and energy management.
            </p>

            <div className="pt-4 flex flex-wrap gap-4">
              <Button
                className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                className="px-6 py-3 bg-white border-2 border-yellow-500 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center space-x-2"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center items-start h-full mt-[-50px]"
            >
              <img
                src="/Portable-Load-Managers-1-removebg-preview.png"
                alt="Power Quality Analyzer"
                className="max-h-[800px] md:max-h-[1000px] lg:max-h-[1400px] w-auto object-contain drop-shadow-2xl transform scale-150 hover:scale-175 transition-transform duration-700 max-w-full"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  model,
  channels,
  image,
  specs,
  isNew = false,
  colors = {
    primary: 'yellow-500',
    secondary: 'yellow-50'
  },
  onViewDetailsClick
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-4 md:p-8 flex justify-center items-center bg-${colors.secondary} h-[300px] md:h-[400px] overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image}
            alt={model}
            className="h-auto max-h-[250px] md:max-h-96 w-auto object-contain z-10 drop-shadow-xl max-w-full"
            animate={{
              y: [0, -15, 0],
            }}
            transition={{
              repeat: Infinity,
              duration: 3,
              ease: "easeInOut"
            }}
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          {isNew && (
            <div className={`absolute top-4 right-4 bg-${colors.primary} text-white text-xs font-bold py-1 px-3 rounded-full`}>
              NEW
            </div>
          )}
          <div className={`absolute top-4 left-4 bg-${colors.primary} text-white text-xs font-bold py-1 px-3 rounded-full`}>
            {model}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
              {model}
            </h3>
            <span className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-xs font-medium">
              {channels}
            </span>
          </div>

          {/* Key Features */}
          <div className="space-y-2">
            {specs.map((spec, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-500 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-800 text-sm font-medium">{spec}</span>
              </div>
            ))}
          </div>

          {/* View Details Button */}
          <Button
            onClick={onViewDetailsClick}
            className={`w-full mt-4 py-3 px-4 bg-${colors.primary} hover:bg-opacity-90 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-6 h-full border-b-4 border-yellow-400"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-4 shadow-md">
          {icon}
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-800 flex-grow">{description}</p>
      </div>
    </motion.div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-3 border-b border-yellow-100">
    <div className="bg-yellow-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-800 font-medium">{text}</span>
  </div>
);

// Expandable section component
const ExpandableSection = ({ title, children, id, isVisible, onToggle }) => {
  return (
    <div className="mb-6 bg-white rounded-2xl shadow-md overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full p-5 flex items-center justify-between bg-gradient-to-r from-yellow-500 to-yellow-400 text-white"
      >
        <h3 className="text-xl font-bold">{title}</h3>
        <ArrowDownCircle
          className={`h-6 w-6 transition-transform duration-300 ${
            isVisible ? "rotate-180" : "rotate-0"
          }`}
        />
      </button>

      <motion.div
        initial={false}
        animate={{ height: isVisible ? "auto" : 0, opacity: isVisible ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        {isVisible && <div className="p-6">{children}</div>}
      </motion.div>
    </div>
  );
};

// CT Table Row with improved design
const CTTableRow = ({
  label,
  data,
  isHeader = false,
  isAlternate = false
}: {
  label: string;
  data: string[];
  isHeader?: boolean;
  isAlternate?: boolean
}) => (
  <div
    className={`grid grid-cols-2 md:grid-cols-7 ${
      isHeader
        ? "py-4 font-bold text-white bg-yellow-500 rounded-t-lg"
        : `py-3 ${isAlternate ? "bg-yellow-50" : "bg-white"}`
    }`}
  >
    <div
      className={`col-span-2 md:col-span-1 px-2 md:px-4 font-medium ${
        isHeader ? "text-white" : "text-yellow-800"
      } ${!isHeader ? "mb-2 md:mb-0" : ""}`}
    >
      {label}
    </div>
    <div className="col-span-2 md:col-span-6 grid grid-cols-3 md:grid-cols-6 gap-1">
      {data.map((item: string, i: number) => (
        <div
          key={i}
          className={`col-span-1 px-1 md:px-4 text-center ${
            isHeader ? "text-white" : "text-gray-800"
          } text-xs md:text-base`}
        >
          {item}
        </div>
      ))}
    </div>
  </div>
);

// Sensor Feature Card for updated design
const SensorFeatureCard = ({
  letter,
  title,
  features
}: {
  letter: string;
  title: string;
  features: string[]
}) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
    whileHover={{ y: -10 }}
    className="relative"
  >
    <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 rounded-t-2xl h-12"></div>
    <div className="relative bg-white p-6 rounded-b-2xl shadow-xl">
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-500 text-xl font-bold">
            {letter}
          </div>
        </div>
      </div>
      <div className="mt-8">
        <h3 className="text-xl font-bold text-yellow-600 text-center mb-6">
          {title}
        </h3>

        <ul className="space-y-4">
          {features.map((feature: string, index: number) => (
            <li key={index} className="flex items-start">
              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
              <span className="text-gray-800">{feature}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </motion.div>
);

// Contact Section Component with improved styling
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-8 px-4 rounded-2xl shadow-lg">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Need More Information?</h2>
        <p className="text-gray-800 mb-8 max-w-3xl mx-auto font-medium">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about the KRYKARD Power Quality Analyzers.
        </p>
        <Button
          className="inline-flex items-center px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );
};

// Main Power Quality Analyzers Component
const PowerQualityAnalyzers = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("alm20");
  const [activeDetailTab, setActiveDetailTab] = useState("features"); // Used in URL params
  const [visibleSection, setVisibleSection] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const productDetailRef = useRef<HTMLDivElement>(null); // Add reference for product detail section  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');
    const detailTab = params.get('detailTab');

    if (tab) setActiveTab(tab);
    if (product) setActiveProductType(product);
    if (detailTab) setActiveDetailTab(detailTab);

    // If directly navigating to a product detail, scroll to it once the component mounts
    if (tab === 'products' && product) {
      // Use requestAnimationFrame instead of setTimeout to prevent blinking
      // This ensures the DOM updates before scrolling
      requestAnimationFrame(() => {
        try {
          // Find the product details section and scroll to it
          const detailSection = document.getElementById('product-detail-section');
          if (detailSection) {
            // Use scrollIntoView with block: 'start' to position at the top of viewport
            detailSection.scrollIntoView({ behavior: 'auto', block: 'start' });
          }
        } catch (e) {
          console.error("Error during scroll:", e);
        }
      });
    }
  }, [location]);

  // Load enhancer script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = '/power-analyzer-enhancer.js';
    script.defer = true;
    script.onload = () => console.log('Enhancer script loaded');
    script.onerror = (e) => console.error('Script loading error:', e);
    document.body.appendChild(script);

    return () => {
      // Clean up script when component unmounts
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };  // Handler for View Details button
  const handleViewDetails = (productType: string) => {
    try {
      // Update state
      setActiveProductType(productType);
      setActiveTab('products');

      // Update URL
      navigate(`?tab=products&product=${productType}`, { replace: true });

      // Use requestAnimationFrame instead of setTimeout to prevent blinking
      // This ensures the DOM updates before scrolling
      requestAnimationFrame(() => {
        try {
          // Find the product details section and scroll to it
          const detailSection = document.getElementById('product-detail-section');
          if (detailSection) {
            // Use scrollIntoView with block: 'start' to position at the top of viewport
            detailSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        } catch (e) {
          console.error("Error during scroll:", e);
        }
      });
    } catch (error) {
      console.error("handleViewDetails error:", error);
      // Fallback - just set the states without URL manipulation
      setActiveProductType(productType);
      setActiveTab('products');
      setActiveDetailTab('overview');
    }
  };

  // ALM20 data
  const alm20Data = {
    model: "ALM 20",
    channels: "6 Channels (3V + 3I)",
    image: "/download-removebg-preview.png",
    specs: [
      "Self-powered via phase",
      "Basic monitoring capabilities",
      "Multiple frequency support",
      "Weeks of measurement recording"
    ],
    hardwareSpecs: [
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "3 Voltage & 3 current Channels"
      },
      {
        icon: <BarChart className="h-5 w-5 text-yellow-500" />,
        text: "Network Frequencies: 50Hz, 60Hz & 400Hz"
      },
      {
        icon: <Gauge className="h-5 w-5 text-yellow-500" />,
        text: "128 samples/cycle"
      },
      {
        icon: <Shield className="h-5 w-5 text-yellow-500" />,
        text: "Self-powered, via the phase"
      }
    ],
    capabilitySpecs: [
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "AC/DC Voltage: Up to 1,000V Ph-N"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "Power: kW, kVAr, kVA, PF & DPF"
      },
      {
        icon: <BarChart className="h-5 w-5 text-yellow-500" />,
        text: "Energy: kWh, kVArh, kVAh"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "THD & Harmonics: Up to 50th order"
      }
    ],
    colors: {
      primary: 'yellow-500',
      secondary: 'yellow-50'
    },
    description: "The ALM 20 provides essential power quality analysis in a compact, self-powered package. Perfect for long-term monitoring and basic power quality assessment."
  };

  // ALM31 data
  const alm31Data = {
    model: "ALM 31",
    channels: "6 Channels (3V + 3I)",
    image: "/images-removebg-preview.png",
    specs: [
      "5.7\" color display",
      "Real-time waveform view",
      "13-hour battery life",
      "Comprehensive measurement"
    ],
    hardwareSpecs: [
      {
        icon: <Shield className="h-5 w-5 text-yellow-500" />,
        text: '5.7" color ¼ VGA TFT screen'
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "Displays real time waveform"
      },
      {
        icon: <Database className="h-5 w-5 text-yellow-500" />,
        text: "Multiple recording intervals"
      },
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "Battery backup up to 13 hours"
      }
    ],
    capabilitySpecs: [
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "AC/DC Voltage: Up to 1,000V Ph-N"
      },
      {
        icon: <BarChart className="h-5 w-5 text-yellow-500" />,
        text: "Power: kW, kVAr, kVA, PF & DPF"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "Crest factor & K-factor measurement"
      },
      {
        icon: <Gauge className="h-5 w-5 text-yellow-500" />,
        text: "Short flicker measurement (Pst)"
      }
    ],
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    },
    description: "The ALM 31 provides real-time visual analysis with its color display and enhanced measurement capabilities for professional power quality assessment."
  };

  // ALM36 data
  const alm36Data = {
    model: "ALM 36",
    channels: "8 Channels (4V + 4I)",
    image: "/download__1_-removebg-preview.png",
    specs: [
      "IEC 61000-4-30 Class B",
      "Advanced alarm system",
      "Transient capture",
      "Up to 2GB memory storage"
    ],
    hardwareSpecs: [
      {
        icon: <Shield className="h-5 w-5 text-yellow-500" />,
        text: "IEC 61000-4-30 Class B compliant"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: '5.7" Color TFT screen display'
      },
      {
        icon: <Database className="h-5 w-5 text-yellow-500" />,
        text: "Memory up to 2 GB"
      },
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "10,000 alarms of 40 conditions"
      }
    ],
    capabilitySpecs: [
      {
        icon: <Gauge className="h-5 w-5 text-yellow-500" />,
        text: "Transients (Up to 210 counts)"
      },
      {
        icon: <BarChart className="h-5 w-5 text-yellow-500" />,
        text: "TrueInrush function"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "THD & Harmonics: Up to 50th order"
      },
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "Pst & Plt flicker measurements"
      }
    ],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-50'
    },
    isNew: true,
    description: "The ALM 36 provides professional-grade power analysis with IEC compliance, advanced alarms, and transient capture for comprehensive quality assessment."
  };

  // CA8345 data
  const ca8345Data = {
    model: "CA 8345 CLASS A",
    channels: "8 Channels (4V + 4I)",
    image: "/download__2_-removebg-preview.png",
    specs: [
      "IEC 61000-4-30 Class A certified",
      "Touch screen interface",
      "64GB memory capacity",
      "Advanced harmonic analysis"
    ],
    hardwareSpecs: [
      {
        icon: <Shield className="h-5 w-5 text-yellow-500" />,
        text: "IEC 61000-4-30 Class A Ed 3.0 certified"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "512 samples/cycle"
      },
      {
        icon: <Gauge className="h-5 w-5 text-yellow-500" />,
        text: '7" color LCD touch screen'
      },
      {
        icon: <Database className="h-5 w-5 text-yellow-500" />,
        text: "Memory up to 64 GB"
      }
    ],
    capabilitySpecs: [
      {
        icon: <BarChart className="h-5 w-5 text-yellow-500" />,
        text: "Harmonics: Up to 127th order"
      },
      {
        icon: <LineChart className="h-5 w-5 text-yellow-500" />,
        text: "Interharmonics: Up to 126th order"
      },
      {
        icon: <Zap className="h-5 w-5 text-yellow-500" />,
        text: "2.5 μs transients capture"
      },
      {
        icon: <Shield className="h-5 w-5 text-yellow-500" />,
        text: "16,362 alarms of 40 conditions"
      }
    ],
    colors: {
      primary: 'yellow-500',
      secondary: 'yellow-50'
    },
    description: "The flagship CA 8345 delivers industry-leading performance with Class A certification, advanced harmonic analysis, and superior storage capacity for the most demanding applications."
  };

  // Product Type Options
  const productOptions = [
    { id: "alm20", label: "ALM 20", models: "Basic Analyzer" },
    { id: "alm31", label: "ALM 31", models: "Standard Analyzer" },
    { id: "alm36", label: "ALM 36", models: "Advanced Analyzer", isNew: true },
    { id: "ca8345", label: "CA 8345", models: "Premium Analyzer" }
  ];
  // Get active product data
  const getActiveProductData = () => {
    try {
      if (activeProductType === "alm20") return alm20Data;
      if (activeProductType === "alm31") return alm31Data;
      if (activeProductType === "alm36") return alm36Data;
      return ca8345Data;
    } catch (error) {
      console.error("Error getting product data:", error);
      return alm20Data; // Fallback to first product
    }
  };

  // Toggle expandable section
  const toggleSection = (sectionId: string) => {
    setVisibleSection(visibleSection === sectionId ? null : sectionId);
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "products", label: "Products", icon: <Zap className="h-5 w-5" /> },
    { id: "ct", label: "CT Specs", icon: <Shield className="h-5 w-5" /> },
    { id: "software", label: "Software", icon: <Database className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Power Quality Analyzers"
      subtitle="Advanced tools for precise electrical measurements"
      category="measure"    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "products") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-80' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "products") {
                      navigate(`?tab=${tab.id}&product=${activeProductType}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview */}
          <div className="py-16 bg-gradient-to-br from-yellow-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-bold text-yellow-600 mb-4">Why Choose Our Analyzers?</h2>
                  <p className="mt-4 text-lg text-gray-800 max-w-3xl mx-auto font-medium">
                    Experience advanced precision and robust performance with our state-of-the-art analyzers designed for professionals
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <FeatureHighlight
                  icon={<Gauge className="h-6 w-6 text-white" />}
                  title="Precision Engineering"
                  description="Industry-leading accuracy with advanced calibration for reliable measurements every time."
                />

                <FeatureHighlight
                  icon={<Shield className="h-6 w-6 text-white" />}
                  title="Robust Design"
                  description="Built to withstand challenging field environments with rugged components and protective features."
                />

                <FeatureHighlight
                  icon={<Database className="h-6 w-6 text-white" />}
                  title="Data Management"
                  description="Comprehensive storage and analysis capabilities for managing extensive measurement data."
                />

                <FeatureHighlight
                  icon={<LineChart className="h-6 w-6 text-white" />}
                  title="Modern Interface"
                  description="Intuitive controls and displays for easy operation, reducing learning curve and improving efficiency."
                />

                <FeatureHighlight
                  icon={<Zap className="h-6 w-6 text-white" />}
                  title="Connectivity"
                  description="Seamless integration with software and systems for enhanced workflow and data sharing."
                />

                <FeatureHighlight
                  icon={<BarChart className="h-6 w-6 text-white" />}
                  title="Long Battery Life"
                  description="Extended operation for field measurements without interruption, ensuring reliable data collection."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm font-semibold mb-4">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-4xl font-bold mb-6 text-gray-900">
                Power Quality Analyzer Series
              </h2>
              <p className="max-w-3xl mx-auto text-gray-800 text-lg font-medium">
                Choose the perfect analyzer for your electrical measurement needs
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
              <ProductCard
                {...alm20Data}
                onViewDetailsClick={() => handleViewDetails("alm20")}
                colors={{ primary: 'yellow-500', secondary: 'yellow-50' }}
              />
              <ProductCard
                {...alm31Data}
                onViewDetailsClick={() => handleViewDetails("alm31")}
                colors={{ primary: 'yellow-500', secondary: 'yellow-50' }}
              />
              <ProductCard
                {...alm36Data}
                onViewDetailsClick={() => handleViewDetails("alm36")}
                colors={{ primary: 'yellow-500', secondary: 'yellow-50' }}
                isNew={true}
              />
              <ProductCard
                {...ca8345Data}
                onViewDetailsClick={() => handleViewDetails("ca8345")}
                colors={{ primary: 'yellow-500', secondary: 'yellow-50' }}
              />
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "products" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Enhanced Product Type Selector - Compact Version */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-6 mb-8 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-xl font-bold text-gray-900 mb-4 relative z-10">
              Select <span className="text-yellow-500">Model Series</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-3 relative z-10">
              {productOptions.map((option) => (
                <motion.button
                  key={option.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}                  onClick={() => {
                    try {
                      setActiveProductType(option.id);
                      navigate(`?tab=products&product=${option.id}`, { replace: true });

                      // Scroll to product details safely
                      setTimeout(() => {
                        if (productDetailRef && productDetailRef.current) {
                          productDetailRef.current.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                          });
                        }
                      }, 250);
                    } catch (error) {
                      console.error("Product selector error:", error);
                      // Try to at least change the product
                      setActiveProductType(option.id);
                    }
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    activeProductType === option.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-4 px-3 flex flex-col items-center text-center ${
                    activeProductType === option.id
                      ? "bg-yellow-500 text-white"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-2">
                      {option.id === "alm20" && (
                        <Gauge className={`h-8 w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-500"}`} />
                      )}
                      {option.id === "alm31" && (
                        <LineChart className={`h-8 w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-500"}`} />
                      )}
                      {option.id === "alm36" && (
                        <BarChart className={`h-8 w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-500"}`} />
                      )}
                      {option.id === "ca8345" && (
                        <Database className={`h-8 w-8 ${activeProductType === option.id ? "text-white" : "text-yellow-500"}`} />
                      )}
                    </div>

                    <h3 className={`text-lg font-bold mb-1 ${activeProductType === option.id ? "text-white" : "text-gray-900"}`}>
                      {option.label}
                    </h3>

                    <div className={`text-xs ${activeProductType === option.id ? "text-white opacity-80" : "text-gray-500"}`}>
                      {option.models}
                    </div>

                    {option.isNew && (
                      <div className="mt-1 bg-yellow-200 text-yellow-800 rounded-full px-2 py-0.5 text-xs font-semibold">
                        NEW
                      </div>
                    )}

                    {activeProductType === option.id && (
                      <div className="mt-2 bg-white bg-opacity-20 rounded-full px-3 py-0.5 text-xs font-semibold">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>          {/* Product Detail Section */}
          <div
            ref={productDetailRef}
            className="product-detail-section relative py-8 md:py-16 bg-gradient-to-br from-yellow-50 to-white rounded-3xl shadow-xl overflow-hidden border border-yellow-100"
            id="product-detail-section"
          >
            <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>

            <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 px-4 md:px-6 overflow-hidden">
              <motion.div
                className={`${activeProductType === "alm31" || activeProductType === "ca8345" ? "lg:order-1" : "lg:order-2"} flex-1 flex justify-center items-center overflow-hidden`}
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="relative overflow-hidden">
                  {activeProductType === "alm36" && (
                    <div className="absolute top-0 right-0 bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium z-10">
                      NEW MODEL
                    </div>
                  )}
                  <motion.img
                    src={getActiveProductData().image}
                    alt={getActiveProductData().model}
                    className="w-auto h-auto object-contain hover:scale-110 transition-transform duration-500 max-h-[700px] max-w-full"
                    animate={{
                      y: [0, -20, 0],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 4,
                      ease: "easeInOut"
                    }}
                  />
                </div>
              </motion.div>

              <motion.div
                className={`${activeProductType === "alm31" || activeProductType === "ca8345" ? "lg:order-2" : "lg:order-1"} flex-1`}
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="flex items-center mb-6">
                  <h2 className="text-3xl font-bold text-yellow-600">{getActiveProductData().model}</h2>
                  <div className="ml-4 bg-yellow-100 text-yellow-700 px-4 py-1 rounded-full text-sm font-medium">
                    {getActiveProductData().channels}
                  </div>
                </div>

                <p className="text-gray-800 mb-8">
                  {getActiveProductData().description}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mb-8">
                  <div className="bg-white p-5 rounded-xl shadow-md">
                    <h3 className="text-xl font-semibold text-yellow-700 mb-4 border-b-2 border-yellow-300 pb-1 inline-block">
                      Hardware
                    </h3>
                    <div className="space-y-3">
                      {getActiveProductData().hardwareSpecs.map((spec, idx) => (
                        <SpecItem
                          key={idx}
                          icon={spec.icon}
                          text={spec.text}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="bg-white p-5 rounded-xl shadow-md">
                    <h3 className="text-xl font-semibold text-yellow-700 mb-4 border-b-2 border-yellow-300 pb-1 inline-block">
                      Capabilities
                    </h3>
                    <div className="space-y-3">
                      {getActiveProductData().capabilitySpecs.map((spec, idx) => (
                        <SpecItem
                          key={idx}
                          icon={spec.icon}
                          text={spec.text}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={handleRequestDemo}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white rounded-xl shadow-md px-6 w-full sm:w-auto"
                  >
                    Request Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    onClick={handleViewBrochure}
                    className="bg-white border border-yellow-200 text-yellow-600 hover:bg-yellow-50 rounded-xl shadow-sm px-6 w-full sm:w-auto"
                  >
                    View Brochure
                    <FileText className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "ct" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            className="relative text-center pb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute inset-0 flex justify-center">
              <div className="w-64 h-16 bg-yellow-300 rounded-full opacity-40 blur-xl"></div>
            </div>
            <h2 className="relative z-10 text-4xl font-bold text-yellow-600 inline-block">
              CT SPECIFICATIONS
            </h2>
            <p className="text-gray-800 mt-4">Choose the right current transformers for your measurement needs</p>
          </motion.div>

          {/* CT specification block */}
          <ExpandableSection
            title="Current Transformer Comparison Chart"
            id="ct-chart"
            isVisible={visibleSection === "ct-chart"}
            onToggle={() => toggleSection("ct-chart")}
          >
            <div className="overflow-x-auto rounded-lg shadow-sm">
              <div className="bg-white border border-yellow-100 rounded-lg overflow-hidden">
                <CTTableRow
                  isHeader={true}
                  label="Specifications"
                  data={[
                    "Ampflex A193 450 mm",
                    "Ampflex A193 800 mm",
                    "MN 93A",
                    "PAC 93",
                    "C 193",
                    "J 93"
                  ]}
                />
                <CTTableRow
                  label="Dia (mm)"
                  data={["140", "250", "20", "39", "52", "72"]}
                  isAlternate={true}
                />
                <CTTableRow
                  label="Maximum Current"
                  data={[
                    "10,000 Aac",
                    "10,000 Aac",
                    "Dual range 5 Aac & 100 Aac",
                    "1,000 Aac/1,300 Adc",
                    "1,000 Aac",
                    "3,500 Aac / 5,000 Adc"
                  ]}
                />
                <CTTableRow
                  label="Range"
                  data={[
                    "100 mA to 10,000 Aac",
                    "100 mA to 10,000 Aac",
                    "5 mA to 5 Aac/ 100 mA to 100 Aac",
                    "1 A to 1,000 Aac / 1 A to 1,300 Adc",
                    "1 A to 1,000 Aac",
                    "50 A to 3,500 Aac / 50 A to 5,000 Adc"
                  ]}
                  isAlternate={true}
                />
                <CTTableRow
                  label="Accuracy"
                  data={["±2%", "±2%", "±0.7%", "±3%", "±0.2%", "±1%"]}
                />
                <CTTableRow
                  label="Safety"
                  data={[
                    "CAT IV 600 V/CAT III 1000 V",
                    "CAT IV 600 V/CAT III 1000 V",
                    "CAT IV 300 V/CAT III 600 V",
                    "CAT IV 300 V/CAT III 600 V",
                    "CAT IV 600 V",
                    "CAT IV 300 V/CAT III 600 V"
                  ]}
                  isAlternate={true}
                />
              </div>
            </div>
          </ExpandableSection>

          {/* Updated Sensor Cards */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <SensorFeatureCard
              letter="F"
              title="Flexible Sensors"
              features={[
                "Ideal for large conductors",
                "Available in multiple lengths (450mm and 800mm)",
                "Up to 10,000 Aac measurement capacity",
                "CAT IV 600V safety rating",
                "Easy to install around multiple conductors",
                "Lightweight and flexible design"
              ]}
            />

            <SensorFeatureCard
              letter="C"
              title="Clamp Sensors"
              features={[
                "Compact design for tight spaces",
                "High accuracy measurements (up to ±0.2%)",
                "Available for AC and AC/DC applications",
                "Various jaw sizes from 20mm to 52mm",
                "Robust construction for field use",
                "Multiple current ranges available"
              ]}
            />

            <SensorFeatureCard
              letter="S"
              title="Specialized Sensors"
              features={[
                "For high current applications",
                "DC capable with large jaw size (72mm)",
                "High safety rating (CAT IV)",
                "Measurement up to 5,000 Adc",
                "Advanced shielding technology",
                "Suitable for industrial environments"
              ]}
            />
          </div>

          {/* Contact Section */}
          <div className="mt-20">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {activeTab === "software" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            className="relative text-center pb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute inset-0 flex justify-center">
              <div className="w-64 h-16 bg-yellow-300 rounded-full opacity-40 blur-xl"></div>
            </div>
            <h2 className="relative z-10 text-4xl font-bold text-yellow-600 inline-block">
              DATAVIEW SOFTWARE
            </h2>
            <p className="text-gray-800 mt-4">
              Powerful analysis tools for KRYKARD POWER QUALITY ANALYZER series
            </p>
          </motion.div>

          {/* Software Showcase - Updated Modern Layout */}
          <div className="relative bg-gradient-to-br from-yellow-50 to-white rounded-3xl shadow-xl overflow-hidden border border-yellow-100 mb-16 py-8">
            <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-96 h-96 bg-yellow-200 rounded-full opacity-20 -z-10 blur-3xl"></div>

            <div className="p-8 md:p-16">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="space-y-8">
                    <div>
                    <h3 className="text-2xl font-bold text-yellow-600 mb-6">Comprehensive Analysis Tools</h3>
                      <p className="text-gray-800 mb-6">
                        DataView software provides powerful tools for analyzing, organizing, and reporting
                        your power quality measurements with ease and precision.
                      </p>

                      <div className="space-y-4">
                        <div className="bg-white p-5 rounded-xl shadow-md border border-yellow-100">
                          <h4 className="text-lg font-semibold text-yellow-700 mb-3 flex items-center">
                            <Database className="h-5 w-5 text-yellow-500 mr-2" />
                            Data Management
                          </h4>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">
                                Recovery of measurement data
                              </span>
                            </li>
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">
                                Backup of measurement files
                              </span>
                            </li>
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">
                                Opening of backup files
                              </span>
                            </li>
                          </ul>
                        </div>

                        <div className="bg-white p-5 rounded-xl shadow-md border border-yellow-100">
                          <h4 className="text-lg font-semibold text-yellow-700 mb-3 flex items-center">
                            <BarChart className="h-5 w-5 text-yellow-500 mr-2" />
                            Reporting Features
                          </h4>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">
                                Export into Excel spreadsheets
                              </span>
                            </li>
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">Export in PDF format</span>
                            </li>
                            <li className="flex items-start">
                              <Check className="h-5 w-5 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
                              <span className="text-gray-800">
                                Comprehensive analysis tools
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="space-y-6 flex-1"
                >
                  <h3 className="text-2xl font-bold text-yellow-600 mb-6">Software Interface</h3>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 h-full overflow-hidden">
                    <motion.div
                      className="relative group overflow-hidden rounded-xl shadow-lg"
                      animate={{
                        y: [0, -10, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                        delay: 0
                      }}
                    >
                      <div className="absolute inset-0 bg-yellow-500 opacity-0 group-hover:opacity-10 transition-opacity"></div>
                      <div className="overflow-hidden">
                        <img
                          src="/b1.jpg"
                          alt="DataView Screenshot 1"
                          className="w-full h-auto min-h-[300px] object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                    </motion.div>
                    <motion.div
                      className="relative group overflow-hidden rounded-xl shadow-lg"
                      animate={{
                        y: [0, -10, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                        delay: 0.5
                      }}
                    >
                      <div className="absolute inset-0 bg-yellow-500 opacity-0 group-hover:opacity-10 transition-opacity"></div>
                      <div className="overflow-hidden">
                        <img
                          src="/b2.jpg"
                          alt="DataView Screenshot 2"
                          className="w-full h-auto min-h-[300px] object-cover transition-transform group-hover:scale-110"
                        />
                      </div>
                    </motion.div>
                    <motion.div
                      className="relative group overflow-hidden rounded-xl shadow-lg"
                      animate={{
                        y: [0, -10, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                        delay: 1
                      }}
                    >
                      <div className="absolute inset-0 bg-yellow-500 opacity-0 group-hover:opacity-10 transition-opacity"></div>
                      <div className="overflow-hidden">
                        <img
                          src="/b6.jpg"
                          alt="DataView Screenshot 3"
                          className="w-full h-auto min-h-[300px] object-cover transition-transform group-hover:scale-110"
                        />
                      </div>
                    </motion.div>
                    <motion.div
                      className="relative group overflow-hidden rounded-xl shadow-lg"
                      animate={{
                        y: [0, -10, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                        delay: 1.5
                      }}
                    >
                      <div className="absolute inset-0 bg-yellow-500 opacity-0 group-hover:opacity-10 transition-opacity"></div>
                      <div className="overflow-hidden">
                        <img
                          src= "/b7.jpg"
                          alt="DataView Screenshot 4"
                          className="w-full h-auto min-h-[300px] object-cover transition-transform group-hover:scale-110"
                        />
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="relative p-8 md:p-12 bg-white rounded-3xl shadow-xl border border-yellow-100"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-50 to-white rounded-3xl"></div>
            <div className="relative z-10">
              <h3 className="text-2xl font-bold text-yellow-600 text-center mb-10">Software Benefits</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <motion.div
                  whileHover={{ y: -5 }}
                  className="bg-gradient-to-br from-yellow-50 to-white rounded-2xl shadow-lg p-6 border-b-4 border-yellow-400"
                >
                  <div className="w-16 h-16 bg-yellow-400 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 shadow-md">
                    <Database className="h-8 w-8" />
                  </div>
                  <h4 className="text-xl font-semibold text-yellow-700 mb-3">
                    Data Management
                  </h4>
                  <p className="text-gray-800">
                    Efficiently organize and access measurement data with powerful database features
                    that streamline your workflow.
                  </p>
                </motion.div>

                <motion.div
                  whileHover={{ y: -5 }}
                  className="bg-gradient-to-br from-yellow-50 to-white rounded-2xl shadow-lg p-6 border-b-4 border-yellow-400"
                >
                  <div className="w-16 h-16 bg-yellow-400 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 shadow-md">
                    <LineChart className="h-8 w-8" />
                  </div>
                  <h4 className="text-xl font-semibold text-yellow-700 mb-3">
                    Advanced Analysis
                  </h4>
                  <p className="text-gray-800">
                    Perform detailed analysis with comprehensive tools for waveform, harmonic, and trend analysis
                    to identify issues and optimize performance.
                  </p>
                </motion.div>

                <motion.div
                  whileHover={{ y: -5 }}
                  className="bg-gradient-to-br from-yellow-50 to-white rounded-2xl shadow-lg p-6 border-b-4 border-yellow-400"
                >
                  <div className="w-16 h-16 bg-yellow-400 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 shadow-md">
                    <BarChart className="h-8 w-8" />
                  </div>
                  <h4 className="text-xl font-semibold text-yellow-700 mb-3">
                    Reporting
                  </h4>
                  <p className="text-gray-800">
                    Generate professional reports with customizable templates for Excel and PDF formats
                    that deliver clear, actionable insights.
                  </p>
                </motion.div>
              </div>

              <div className="mt-10 flex justify-center">
                <Button
                  onClick={handleRequestDemo}
                  className="px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition-colors duration-300"
                >
                  Request Software Demo
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          </motion.div>

          {/* Contact Section */}
          <div className="mt-20">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}



      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default PowerQualityAnalyzers;