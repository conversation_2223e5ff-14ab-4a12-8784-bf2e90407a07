import React, { useState } from 'react';
import { ChevronRight, Info, Check, ArrowUpRight, Award, Zap, Shield, Clock, BarChart3, ArrowRight, FileText } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { motion } from 'framer-motion';

const ProductSpecification = () => {
  const [activeTab, setActiveTab] = useState('features');
  const [hoveredModel, setHoveredModel] = useState(null);

  const tabs = [
    { id: 'features', label: 'Features' },
    { id: 'advantages', label: 'Advantages' },
    { id: 'benefits', label: 'Benefits' }
  ];

  const featuresList = [
    { title: 'Wide input voltage range (115 - 280 VAC)', desc: 'Protects against unstable input' },
    { title: 'DSP technology for flexible operation', desc: '' },
    { title: 'Scalable footprint design', desc: '' },
    { title: 'Complete tool-free maintenance with zero function downtime for easy access on site during', desc: '' },
    { title: 'Frequency range (40 - 70 Hz)', desc: 'Immune to unstable sources' },
    { title: 'Battery connectivity', desc: '' },
    { title: 'Modular configurable control', desc: '' },
    { title: 'Parallel capability & high-tier load capacity', desc: '' },
    { title: 'Online Double conversion with Advanced dual-core DSP control technology', desc: 'Full Digital control for highest performance' },
    { title: 'Self protecting with 3 level design', desc: '' },
    { title: 'Advanced Battery Management', desc: 'Automatic battery test including deep discharge protection' },
    { title: 'Software compatibility with connectivity', desc: '' },
    { title: 'CSA/special protection requirements with temperature compensation', desc: '' },
    { title: 'Complete solutions for unmatched application', desc: '' },
    { title: 'Transformerless Design', desc: '' },
    { title: 'Advanced Power Factor Correction (> 0.99) for PF', desc: '' },
    { title: 'Low Technical Load', desc: '' },
    { title: 'Compact internal layout', desc: '' }
  ];

  const advantagesList = [
    { title: 'Maintenance Bypass Switch (optional)', desc: 'Inbuilt Battery Cabinet' },
    { title: 'Single, extended Battery Set', desc: '' },
    { title: 'Current Generator Overload due to starting inrush currents', desc: '' },
    { title: 'Advanced backfeed protection options', desc: '' },
    { title: 'On-line double Conversion & full Digital Frequency Converter', desc: 'Complete emergency coverage mode. Output stable frequency irrespective of input (220V / 230V / 240V)' },
    { title: 'Built-in system protection diagnostic', desc: 'SNMP / USB Option compatibility' },
    { title: 'Advance backfeed protection circuit design', desc: 'Various operating modes access diverse' },
    { title: 'Power protection concept (with regenerating capability for critical loads)', desc: 'Including wide power sensor under varying load conditions with adjustable response' },
    { title: 'Output frequency freely selectable', desc: 'For sensitive loads and industrial equipment' },
    { title: 'Built-in DC fuses', desc: 'Advance Battery based analyzer function' },
    { title: 'Built-in internal battery life extender and capacity for redundancy & load stability', desc: '' },
    { title: 'Short circuit limitation', desc: '' },
    { title: 'Enhanced bypass fix', desc: '' },
    { title: 'Protection against Short-Circuit, Overload, Over temperature, Surge protection, Voltage, Var control & Output for Safety', desc: '' },
    { title: 'Low Operating Cost', desc: 'High Efficiency - Upto 95% in offline mode, 93% in online mode' },
    { title: 'Reduction in carbon footprint', desc: 'Smaller sizes than legacy systems' },
    { title: 'Maximum utilization of UPS capacity', desc: '' },
    { title: 'Better efficiency related to lower heat, saving up to 40%', desc: '' },
    { title: 'Convenient floor space', desc: '' }
  ];

  const benefitsList = [
    {
      title: 'High Uptime / Availability',
      desc: 'Ensures your critical systems remain operational with minimal to zero interruption, delivering maximum operational continuity.'
    },
    {
      title: 'High Flexibility',
      desc: 'Adapts to various configurations and settings, supporting a wide range of devices and operational environments.'
    },
    {
      title: 'High Reliability',
      desc: 'Engineered with premium components and advanced protection systems to ensure consistent and dependable performance.'
    },
    {
      title: 'Low Total Cost of Ownership (TCO)',
      desc: 'Combines energy efficiency, extended lifespan, and reduced maintenance requirements to minimize long-term costs.'
    }
  ];

  const specifications = [
    { category: 'MODEL', model1: 'EH 11 - 6K', model2: 'EH 11 - 10K' },
    { category: 'Rated Capacity', model1: '6 kVA / 6 kW', model2: '10 kVA / 10 kW' },
    { category: 'INPUT', model1: '', model2: '' },
    { category: 'Phase', model1: 'Single phase (single-wire)', model2: 'Single phase (single-wire)' },
    { category: 'Rated Voltage', model1: '220 / 230 / 240 VAC', model2: '220 / 230 / 240 VAC' },
    { category: 'Voltage Range', model1: '115 ~ 175 VAC (linear de-rating between 50% to 100% load); 175 ~ 280 VAC (no de-rating)', model2: '115 ~ 175 VAC (linear de-rating between 50% to 100% load); 175 ~ 280 VAC (no de-rating)' },
    { category: 'Rated Frequency', model1: '50 / 60 Hz (auto-sensing)', model2: '50 / 60 Hz (auto-sensing)' },
    { category: 'Frequency Range', model1: '40 ~ 70 Hz', model2: '40 ~ 70 Hz' },
    { category: 'Power Factor', model1: '≥ 0.99', model2: '≥ 0.99' },
    { category: 'Bypass Voltage Range', model1: '-40% ~ +15% (adjustable)', model2: '-40% ~ +15% (adjustable)' },
    { category: 'Current Harmonic Distortion (THDi)', model1: '< 3%', model2: '< 3%' },
    { category: 'OUTPUT', model1: '', model2: '' },
    { category: 'Output Wiring', model1: 'Single phase (three-wire) (1Ph + N + PE)', model2: 'Single phase (three-wire) (1Ph + N + PE)' },
    { category: 'Rated Voltage', model1: '220 / 230 / 240 VAC (configurable via LCD)', model2: '220 / 230 / 240 VAC (configurable via LCD)' },
    { category: 'Voltage Regulation', model1: '±1%', model2: '±1%' },
    { category: 'Frequency', model1: 'Synchronized with bypass in mains mode; 50/60 Hz ±0.1% in battery mode', model2: 'Synchronized with bypass in mains mode; 50/60 Hz ±0.1% in battery mode' },
    { category: 'Waveform', model1: 'Sinusoidal', model2: 'Sinusoidal' },
    { category: 'Power Factor', model1: '1', model2: '1' },
    { category: 'Total Harmonic Distortion (THDv)', model1: '< 1% (linear load), < 5% (non-linear load)', model2: '< 1% (linear load), < 5% (non-linear load)' },
    { category: 'Crest Factor', model1: '3:1', model2: '3:1' },
    { category: 'Overload', model1: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec', model2: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec' },
    { category: 'BATTERIES', model1: '', model2: '' },
    { category: 'DC Voltage', model1: '192 VDC (16x 12V sealed)', model2: '240 VDC (20x 12V sealed)' },
    { category: 'No. of Batteries', model1: '16 pcs (12V)', model2: '20 pcs (12V)' },
    { category: 'Charging Current', model1: '1A (adjustable)', model2: '1A (adjustable)' },
    { category: 'Recharge Time', model1: '8 hrs (depends on capacity of battery)', model2: '8 hrs (depends on capacity of battery)' },
    { category: 'DISPLAY', model1: '', model2: '' },
    { category: 'LCD Function', model1: 'Load level, Battery level, Input voltage, Output voltage, Operation mode', model2: 'Load level, Battery level, Input voltage, Output voltage, Operation mode' },
    { category: 'Protection', model1: 'Short circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage & Fan failure', model2: 'Short circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage & Fan failure' },
    { category: 'Max. no. of Parallel Connections', model1: 'RS-232 (standard) + Dry / AS400 / dry contact / SNMP / battery temperature compensation (optional)', model2: 'RS-232 (standard) + Dry / AS400 / dry contact / SNMP / battery temperature compensation (optional)' },
    { category: 'Communications', model1: 'LED / LCD', model2: 'LED / LCD' },
    { category: 'Display', model1: 'LED / LCD', model2: 'LED / LCD' },
    { category: 'OTHERS', model1: '', model2: '' },
    { category: 'Operating Temperature', model1: '0°C ~ 40°C', model2: '0°C ~ 40°C' },
    { category: 'Storage Temperature', model1: '-25°C ~ 55°C (without battery)', model2: '-25°C ~ 55°C (without battery)' },
    { category: 'Relative Humidity', model1: '0% ~ 95% (non-condensing)', model2: '0% ~ 95% (non-condensing)' },
    { category: 'Altitude', model1: '< 1000 m, derating 1% for each additional 100 m', model2: '< 1000 m, derating 1% for each additional 100 m' },
    { category: 'IP rating', model1: 'IP 20', model2: 'IP 20' },
    { category: 'Noise level at 1 m', model1: '< 55 dB', model2: '< 58 dB' },
    { category: 'Dimensions (W × D × H) (mm)', model1: '190 × 468 × 720', model2: '231 × 640 × 750' },
    { category: 'Net Weight (kg)', model1: '53', model2: '83' }
  ];

  const renderContent = () => {
    switch(activeTab) {
      case 'features':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
            {featuresList.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white p-3 sm:p-4 md:p-6 rounded-xl border border-blue-100 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Decorative gradient accent */}
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-500 to-blue-700"></div>

                <div className="flex items-start gap-2 sm:gap-3 md:gap-4">
                  <div className="mt-1 text-blue-600 bg-blue-50 p-1.5 sm:p-2 rounded-full">
                    <Check size={16} className="text-blue-600 sm:hidden" />
                    <Check size={18} className="text-blue-600 hidden sm:block" />
                  </div>
                  <div>
                    <h4 className="font-bold text-sm sm:text-base md:text-lg text-blue-800 mb-1 sm:mb-2">{feature.title}</h4>
                    {feature.desc && <p className="text-blue-700 text-xs sm:text-sm md:text-base">{feature.desc}</p>}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );
      case 'advantages':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
            {advantagesList.map((advantage, index) => (
              <motion.div
                key={index}
                className="bg-white p-3 sm:p-4 md:p-6 rounded-xl border border-blue-100 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Decorative gradient accent */}
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-600 to-blue-400"></div>

                <div className="flex items-start gap-2 sm:gap-3 md:gap-4">
                  <div className="mt-1 text-blue-600 bg-blue-50 p-1.5 sm:p-2 rounded-full">
                    <ArrowUpRight size={16} className="text-blue-600 sm:hidden" />
                    <ArrowUpRight size={18} className="text-blue-600 hidden sm:block" />
                  </div>
                  <div>
                    <h4 className="font-bold text-sm sm:text-base md:text-lg text-blue-800 mb-1 sm:mb-2">{advantage.title}</h4>
                    {advantage.desc && <p className="text-blue-700 text-xs sm:text-sm md:text-base">{advantage.desc}</p>}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );
      case 'benefits':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
            {benefitsList.map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-white p-4 sm:p-6 md:p-8 rounded-xl border border-blue-100 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Background decorative elements */}
                <div className="absolute top-0 right-0 w-24 sm:w-28 md:w-32 h-24 sm:h-28 md:h-32 bg-blue-50 opacity-40 rounded-full transform translate-x-12 sm:translate-x-16 -translate-y-12 sm:-translate-y-16 z-0"></div>

                <div className="relative z-10">
                  <div className="flex items-center gap-2 sm:gap-3 md:gap-4 mb-2 sm:mb-3 md:mb-4">
                    <div className="text-blue-600 bg-blue-50 p-2 sm:p-2.5 md:p-3 rounded-full">
                      <Award size={18} className="text-blue-600 sm:hidden" />
                      <Award size={20} className="text-blue-600 hidden sm:block md:hidden" />
                      <Award size={24} className="text-blue-600 hidden md:block" />
                    </div>
                    <h3 className="font-bold text-base sm:text-xl md:text-2xl text-blue-800">{benefit.title}</h3>
                  </div>

                  <p className="text-blue-700 text-xs sm:text-sm md:text-base pl-8 sm:pl-12 md:pl-16">{benefit.desc}</p>
                </div>
              </motion.div>
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  // Stats for Key Features Section
  const keyStats = [
    { value: "95", suffix: "%", title: "Max Efficiency", icon: <Zap size={24} /> },
    { value: "0", suffix: "ms", title: "Transfer Time", icon: <Clock size={24} /> },
    { value: "1.0", suffix: "", title: "Power Factor", icon: <Shield size={24} /> },
    { value: "24/7", suffix: "", title: "Protection", icon: <BarChart3 size={24} /> }
  ];

  // PDF URL for brochure
  const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";
  const ProductSpecContent = () => (
    <div className="w-full mx-auto font-sans">
      {/* Hero Section with Image on Left and Content on Right */}
      <section className="py-16 relative overflow-hidden">
        <div className="relative z-10 px-4 max-w-7xl mx-auto">
          {/* Main Title Centered in Middle of Page */}
          <motion.div
            className="text-center text-blue-800 p-4 sm:p-6 md:p-8 overflow-hidden relative mb-8 sm:mb-12 md:mb-16"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <div className="relative z-10">
              <motion.h1
                className="text-2xl sm:text-3xl md:text-5xl font-extrabold tracking-tight mb-2 sm:mb-3 md:mb-4 text-blue-800"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                KRYKARD EH 11 SERIES <span className="text-blue-600">1/1 UPS</span>
              </motion.h1>

              <motion.p
                className="text-base sm:text-lg md:text-2xl font-medium mb-4 sm:mb-6 md:mb-8 text-blue-600"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
              >
                6 kVA & 10 kVA - Robust backup solutions with compact footprint
              </motion.p>

              <motion.div
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-2 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300 text-xs sm:text-sm md:text-base"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                SMALLER FOOTPRINT WITH ROBUST BACKUP SOLUTIONS
              </motion.div>
            </div>
          </motion.div>

          {/* Hero Content Area - Left image, Right content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center mb-8 sm:mb-12 md:mb-16">            {/* Left side: UPS Image */}
            <motion.div
              className="relative flex justify-center order-2 lg:order-1"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                className="w-full max-w-3xl h-[300px] sm:h-[500px] md:h-[700px] flex items-center justify-center"
                animate={{
                  y: [0, -20, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              >
                {/* Replace the src attribute with your actual image URL */}
                <img
                  src="/UPS/SB4_-_2-removebg-preview.png"
                  alt="EH 11 Series UPS"
                  className="max-w-full max-h-full object-contain drop-shadow-2xl transform hover:scale-115 transition-transform duration-700"
                  style={{ filter: "drop-shadow(0 20px 30px rgba(0, 0, 0, 0.3))" }}
                />
              </motion.div>
            </motion.div>

            {/* Right side: Content */}
            <motion.div
              className="space-y-4 sm:space-y-6 md:space-y-8 order-1 lg:order-2"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-900 mb-2 sm:mb-3 md:mb-4">Enterprise-Grade Power Protection</h2>
                <div className="h-1 sm:h-1.5 w-16 sm:w-20 md:w-24 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-3 sm:mb-4 md:mb-6"></div>
                <p className="text-sm sm:text-base md:text-lg text-blue-800 leading-relaxed">
                  The KRYKARD EH 11 Series UPS delivers reliable power protection for your mission-critical equipment with its double conversion technology, ensuring continuous operation during power disturbances with advanced technology and robust engineering.
                </p>
              </motion.div>

              <motion.div
                className="bg-gradient-to-r from-transparent to-blue-50 p-3 sm:p-4 md:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h3 className="text-base sm:text-lg md:text-xl font-bold mb-2 sm:mb-3 md:mb-4 text-blue-800">Perfect for:</h3>
                <ul className="space-y-2 sm:space-y-3 md:space-y-4">
                  {[
                    {icon: "🏢", text: "Small to Medium Businesses"},
                    {icon: "🏥", text: "Medical Equipment & Healthcare Facilities"},
                    {icon: "💻", text: "IT Infrastructure & Network Equipment"},
                    {icon: "🏭", text: "Industrial Control Systems"},
                    {icon: "🔌", text: "Sensitive Electronic Equipment"}
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className="flex items-center group"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    >
                      <motion.span
                        className="text-lg sm:text-xl md:text-2xl mr-2 sm:mr-3 md:mr-4 transform group-hover:scale-110 transition-transform"
                        animate={{ rotate: [0, 8, 0] }}
                        transition={{ duration: 6, repeat: Infinity, repeatType: "reverse" }}
                      >
                        {item.icon}
                      </motion.span>
                      <span className="text-blue-700 font-medium group-hover:text-blue-600 transition-colors text-xs sm:text-sm md:text-base">
                        {item.text}
                      </span>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>

              <motion.div
                className="flex flex-wrap gap-3 sm:gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.a
                  href="/contact/sales"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg flex items-center gap-1 sm:gap-2 transition-all duration-300 text-xs sm:text-sm md:text-base"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Request Quote</span>
                  <ArrowRight size={14} className="sm:hidden" />
                  <ArrowRight size={16} className="hidden sm:block md:hidden" />
                  <ArrowRight size={18} className="hidden md:block" />
                </motion.a>

                <motion.button
                  className="border-2 border-blue-600 text-blue-700 hover:bg-blue-50 px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 md:py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm md:text-base"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.open(pdfUrl, "_blank")}
                >
                  <FileText size={14} className="sm:hidden" />
                  <FileText size={16} className="hidden sm:block md:hidden" />
                  <FileText size={18} className="hidden md:block" />
                  <span>View Brochure</span>
                </motion.button>
              </motion.div>
            </motion.div>
          </div>

          {/* Key Features Section - Enhanced Modern Design */}
          <div className="mb-16">
            <motion.div
              className="text-center mb-12"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-blue-900 mb-3 inline-block relative">
                  Key Features
                  <motion.div
                    className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                    initial={{ scaleX: 0, opacity: 0 }}
                    whileInView={{ scaleX: 1, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    viewport={{ once: true }}
                  />
                </h2>
              </motion.div>
              <motion.p
                className="mt-6 text-xl text-blue-700 max-w-2xl mx-auto font-medium"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Core capabilities that define our UPS solutions
              </motion.p>
            </motion.div>

            {/* Modern 3D Card Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
              {keyStats.map((stat, index) => {
                // Define different gradient colors for each card
                const gradients = [
                  "from-blue-500 to-blue-600",
                  "from-indigo-500 to-blue-500",
                  "from-blue-600 to-indigo-600",
                  "from-blue-600 to-blue-700"
                ];

                // Define different glow effects for each card
                const glows = [
                  "from-blue-400/20 via-blue-500/10 to-transparent",
                  "from-indigo-400/20 via-blue-500/10 to-transparent",
                  "from-blue-500/20 via-indigo-500/10 to-transparent",
                  "from-blue-600/20 via-blue-700/10 to-transparent"
                ];

                return (
                  <motion.div
                    key={index}
                    className="bg-white rounded-2xl shadow-xl relative overflow-hidden group"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: index * 0.15 }}
                    viewport={{ once: true }}
                    whileHover={{
                      y: -10,
                      transition: { duration: 0.3 }
                    }}
                  >
                    {/* Top gradient bar */}
                    <div className={`h-2 bg-gradient-to-r ${gradients[index]}`}></div>

                    {/* Background glow effect on hover */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${glows[index]} opacity-0 group-hover:opacity-100 transition-opacity duration-700`}></div>

                    {/* Content */}
                    <div className="p-4 sm:p-6 md:p-8 relative z-10">
                      {/* Animated icon with gradient background */}
                      <motion.div
                        className={`w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-xl sm:rounded-2xl bg-gradient-to-r ${gradients[index]} flex items-center justify-center text-white mb-3 sm:mb-4 md:mb-6 shadow-lg mx-auto`}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: "spring", stiffness: 300, damping: 10 }}
                      >
                        {React.cloneElement(stat.icon, {
                          size: typeof window !== 'undefined' && window.innerWidth < 640 ? 20 :
                                 typeof window !== 'undefined' && window.innerWidth < 768 ? 24 : 32
                        })}
                      </motion.div>

                      {/* Feature value with animated counting effect */}
                      <motion.div
                        className="mb-2 sm:mb-3 md:mb-4 text-center"
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className="flex items-center justify-center">
                          <motion.span
                            className="text-3xl sm:text-4xl md:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                            viewport={{ once: true }}
                          >
                            {stat.value}
                          </motion.span>
                          <motion.span
                            className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-600 ml-1"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            transition={{ duration: 1, delay: 0.7 + index * 0.2 }}
                            viewport={{ once: true }}
                          >
                            {stat.suffix}
                          </motion.span>
                        </div>
                      </motion.div>

                      {/* Feature title with hover effect */}
                      <motion.h3
                        className="text-sm sm:text-base md:text-xl font-bold text-center text-blue-800 group-hover:text-blue-600 transition-colors duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        {stat.title}
                      </motion.h3>

                      {/* Decorative dots */}
                      <div className="flex justify-center mt-2 sm:mt-3 md:mt-4 space-x-1">
                        <motion.div
                          className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 0 }}
                        />
                        <motion.div
                          className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
                        />
                        <motion.div
                          className={`w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-gradient-to-r ${gradients[index]}`}
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Modern Tabs Section with Enhanced Design */}
      <section className="max-w-7xl mx-auto px-4 mb-20 relative">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-blue-900 mb-3 inline-block relative">
              Product Information
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
        </motion.div>

        {/* Modern Glassmorphism Tab Buttons */}
        <div className="flex flex-wrap justify-center mb-8 sm:mb-12 relative z-10">
          <motion.div
            className="inline-flex flex-wrap justify-center gap-2 backdrop-blur-md bg-white/30 p-2 rounded-2xl shadow-xl border border-white/20 mb-6 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {/* Background gradient effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-blue-500/10 opacity-50"></div>

            {/* Animated background particles */}
            <div className="absolute -top-4 left-10 w-12 h-12 bg-blue-400 rounded-full opacity-10 blur-xl"></div>
            <div className="absolute bottom-2 right-10 w-20 h-20 bg-indigo-400 rounded-full opacity-10 blur-xl"></div>

            {tabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                className={`relative py-2 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 font-medium text-sm sm:text-base md:text-lg transition-all duration-300 rounded-xl z-10 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg'
                    : 'text-blue-700 hover:text-blue-900 hover:bg-white/50'
                }`}
                onClick={() => setActiveTab(tab.id)}
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.4)"
                }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.4,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 200
                }}
              >
                {/* Icon indicators for each tab */}
                <div className="flex items-center gap-2 sm:gap-3">
                  {tab.id === 'features' && (
                    <motion.div
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <Check size={16} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} sm:hidden`} />
                      <Check size={18} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} hidden sm:block`} />
                    </motion.div>
                  )}
                  {tab.id === 'advantages' && (
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <ArrowUpRight size={16} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} sm:hidden`} />
                      <ArrowUpRight size={18} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} hidden sm:block`} />
                    </motion.div>
                  )}
                  {tab.id === 'benefits' && (
                    <motion.div
                      animate={{ y: [0, -3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <Award size={16} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} sm:hidden`} />
                      <Award size={18} className={`${activeTab === tab.id ? "text-white" : "text-blue-500"} hidden sm:block`} />
                    </motion.div>
                  )}
                  <span>{tab.label}</span>
                </div>

                {/* Active indicator dot */}
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-white rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    layoutId="activeTabIndicator"
                  />
                )}
              </motion.button>
            ))}
          </motion.div>
        </div>

        {/* Enhanced Tab Content Container with Animation */}
        <motion.div
          className="p-4 sm:p-6 md:p-10 bg-white rounded-3xl shadow-xl border border-blue-100 overflow-hidden relative"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-blue-50 rounded-full opacity-30 transform translate-x-10 sm:translate-x-20 -translate-y-10 sm:-translate-y-20"></div>
          <div className="absolute bottom-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-blue-50 rounded-full opacity-30 transform -translate-x-10 sm:-translate-x-20 translate-y-10 sm:translate-y-20"></div>

          {/* Content with page transition */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{
              duration: 0.5,
              type: "spring",
              stiffness: 100
            }}
            className="relative z-10"
          >
            {renderContent()}
          </motion.div>
        </motion.div>
      </section>

      {/* Enhanced Specifications Table Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20 relative">
        {/* Decorative background elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-40 right-0 w-64 h-64 bg-blue-50 rounded-full opacity-40 blur-xl"></div>
          <div className="absolute bottom-20 left-10 w-80 h-80 bg-indigo-50 rounded-full opacity-30 blur-xl"></div>
        </div>

        <motion.div
          className="text-center mb-12 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-3 inline-block relative">
              Technical Specifications
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-6 text-xl text-gray-800 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Comprehensive technical details for the EH 11 Series UPS line
          </motion.p>
        </motion.div>

        {/* Professional Model Selection Tabs */}
        <motion.div
          className="relative z-10 mb-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex justify-center flex-wrap">
            <div className="inline-flex flex-wrap justify-center gap-2 bg-white p-1.5 rounded-xl shadow-lg border border-gray-100 mb-6">
              {['All Models', 'EH 11 - 6K', 'EH 11 - 10K'].map((model, index) => {
                const modelKey = index === 0 ? null : `model${index}`;
                const isActive = hoveredModel === modelKey || (index === 0 && hoveredModel === null);

                return (
                  <motion.button
                    key={index}
                    className={`relative py-2 sm:py-3 px-3 sm:px-6 font-medium text-sm sm:text-base rounded-lg transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md'
                        : 'text-gray-800 hover:text-blue-900 hover:bg-blue-50'
                    }`}
                    onClick={() => setHoveredModel(index === 0 ? null : `model${index}`)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-center gap-1 sm:gap-2">
                      {isActive && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 500, damping: 15 }}
                        >
                          <Check size={14} className="text-white sm:hidden" />
                          <Check size={16} className="text-white hidden sm:block" />
                        </motion.div>
                      )}
                      <span>{model}</span>
                    </div>

                    {/* Active indicator line */}
                    {isActive && (
                      <motion.div
                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"
                        layoutId="activeModelIndicator"
                      />
                    )}
                  </motion.button>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* Enhanced Interactive Table with Professional Design */}
        <motion.div
          className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 relative z-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
        >
          {/* Table header decoration */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500"></div>

          <div className="overflow-x-auto">
            <div className="text-xs text-blue-600 mb-2 md:hidden">
              <span>← Swipe horizontally to view all specifications →</span>
            </div>
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gradient-to-r from-blue-800 to-blue-900 text-white">
                  <th className="py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 text-left font-bold text-xs sm:text-sm md:text-base uppercase tracking-wider">Specifications</th>
                  {specifications[0].model1 &&
                    <th className="py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 text-left font-bold text-xs sm:text-sm md:text-base uppercase tracking-wider relative">
                      <div
                        className={`absolute inset-0 bg-blue-700 opacity-0 transition-opacity duration-200 ${hoveredModel === 'model1' ? 'opacity-30' : ''}`}
                      />
                      <div
                        className="relative z-10 flex items-center"
                        onMouseEnter={() => setHoveredModel('model1')}
                        onMouseLeave={() => setHoveredModel(null)}
                      >
                        <span>{specifications[0].model1}</span>
                        {hoveredModel === 'model1' && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-2 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-white rounded-full"
                          />
                        )}
                      </div>
                    </th>
                  }
                  {specifications[0].model2 &&
                    <th className="py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 text-left font-bold text-xs sm:text-sm md:text-base uppercase tracking-wider relative">
                      <div
                        className={`absolute inset-0 bg-blue-700 opacity-0 transition-opacity duration-200 ${hoveredModel === 'model2' ? 'opacity-30' : ''}`}
                      />
                      <div
                        className="relative z-10 flex items-center"
                        onMouseEnter={() => setHoveredModel('model2')}
                        onMouseLeave={() => setHoveredModel(null)}
                      >
                        <span>{specifications[0].model2}</span>
                        {hoveredModel === 'model2' && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-2 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-white rounded-full"
                          />
                        )}
                      </div>
                    </th>
                  }
                </tr>
              </thead>
              <tbody>
                {specifications.slice(1).map((spec, index) => {
                  const isHeader = spec.category.includes('INPUT') ||
                                   spec.category.includes('OUTPUT') ||
                                   spec.category.includes('BATTERY') ||
                                   spec.category.includes('DISPLAY') ||
                                   spec.category.includes('OTHERS');

                  return (
                    <motion.tr
                      key={index}
                      className={`border-b ${isHeader ? 'border-gray-200 bg-gray-50' : 'border-gray-100'} hover:bg-blue-50/30 transition-colors duration-300`}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.01 }}
                      viewport={{ once: true, margin: "-100px" }}
                    >
                      <td className={`sticky left-0 bg-white z-10 py-2 sm:py-3 md:py-4 px-3 sm:px-4 md:px-6 ${isHeader ? 'bg-gray-50' : ''}`}>
                        <div className={`${
                          isHeader
                            ? 'font-bold text-gray-900 text-xs sm:text-sm md:text-base'
                            : 'font-medium text-gray-800 text-xs sm:text-sm md:text-base'
                        } ${isHeader ? 'pl-0' : 'pl-2 sm:pl-3 md:pl-4'}`}>
                          {isHeader ? (
                            <div className="flex items-center">
                              <div className="w-1 sm:w-1.5 md:w-2 h-6 sm:h-7 md:h-8 bg-blue-600 rounded-r-md mr-2 sm:mr-3"></div>
                              <span className="uppercase tracking-wide text-xs sm:text-sm">{spec.category}</span>
                            </div>
                          ) : spec.category}
                        </div>
                      </td>
                      {spec.model1 !== undefined &&
                        <td className={`py-2 sm:py-3 md:py-4 px-3 sm:px-4 md:px-6 text-gray-800 font-medium text-xs sm:text-sm md:text-base ${hoveredModel === 'model1' ? 'bg-blue-50' : ''}`}>
                          {spec.model1}
                        </td>
                      }
                      {spec.model2 !== undefined &&
                        <td className={`py-2 sm:py-3 md:py-4 px-3 sm:px-4 md:px-6 text-gray-800 font-medium text-xs sm:text-sm md:text-base ${hoveredModel === 'model2' ? 'bg-blue-50' : ''}`}>
                          {spec.model2}
                        </td>
                      }
                    </motion.tr>
                  );
                })}
              </tbody>
            </table>
          </div>


        </motion.div>
      </section>

      {/* Key Features Highlight Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl font-bold text-blue-900 mb-3">Key Highlights</h2>
          <div className="h-1.5 w-32 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto">
            Standout features that make the EH 11 Series exceptional
          </p>
        </motion.div>

        {/* Enhanced Feature Cards with 3D effects */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 md:gap-8 relative z-10">
          {[
            {
              icon: <Zap size={28} />,
              value: "Unity",
              suffix: "",
              title: "Power Factor",
              description: "1.0 power factor ensures that the kVA rating equals the kW rating, maximizing the efficiency of your power protection investment",
              color: "from-blue-500 to-blue-600",
              bgGlow: "from-blue-400/20 via-blue-500/10 to-transparent"
            },
            {
              icon: <Shield size={28} />,
              value: "115-280",
              suffix: "VAC",
              title: "Input Voltage Range",
              description: "Operates in environments with unstable power conditions without switching to battery mode, extending battery life",
              color: "from-green-500 to-blue-500",
              bgGlow: "from-green-400/20 via-blue-500/10 to-transparent"
            },
            {
              icon: <Clock size={28} />,
              value: "Compact",
              suffix: "",
              title: "Form Factor",
              description: "Smaller footprint than traditional tower UPS systems of similar capacity, ideal for space-constrained environments",
              color: "from-blue-600 to-indigo-600",
              bgGlow: "from-blue-500/20 via-indigo-500/10 to-transparent"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl shadow-xl relative overflow-hidden group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: index * 0.2 }}
              viewport={{ once: true, margin: "-100px" }}
              whileHover={{
                y: -10,
                transition: { duration: 0.3 }
              }}
            >
              {/* Top gradient bar */}
              <div className={`h-2 bg-gradient-to-r ${feature.color}`}></div>

              {/* Background glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGlow} opacity-0 group-hover:opacity-100 transition-opacity duration-700`}></div>

              {/* Content */}
              <div className="p-4 sm:p-6 md:p-8 relative z-10">
                {/* Icon with gradient background */}
                <div className={`w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-xl sm:rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-3 sm:mb-4 md:mb-6 shadow-lg transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}>
                  {React.cloneElement(feature.icon, {
                    size: typeof window !== 'undefined' && window.innerWidth < 640 ? 18 :
                           typeof window !== 'undefined' && window.innerWidth < 768 ? 20 : 24
                  })}
                </div>

                {/* Feature title */}
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-blue-800 mb-1 sm:mb-2">{feature.title}</h3>

                {/* Feature value with animated counting effect */}
                {feature.value && (
                  <motion.div
                    className="mb-2 sm:mb-3 md:mb-4"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center">
                      <motion.span
                        className="text-2xl sm:text-3xl md:text-4xl font-extrabold text-blue-700"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                        viewport={{ once: true }}
                      >
                        {feature.value}
                      </motion.span>
                      <motion.span
                        className="text-lg sm:text-xl md:text-2xl font-bold text-blue-600 ml-1"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 1, delay: 0.7 + index * 0.2 }}
                        viewport={{ once: true }}
                      >
                        {feature.suffix}
                      </motion.span>
                    </div>
                  </motion.div>
                )}

                {/* Feature description */}
                <p className="text-blue-600 text-xs sm:text-sm md:text-base">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="text-xl font-bold text-blue-800 mb-4">Advanced Battery Management</h3>
            <p className="text-blue-700 mb-4">
              The sophisticated battery management system performs automatic tests and deep discharge protection, extending battery life while ensuring optimal backup performance when needed most.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-600">
                <div className="font-bold">Battery Configuration</div>
                <div>EH 11 - 6K: 192 VDC (16x 12V)</div>
                <div>EH 11 - 10K: 240 VDC (20x 12V)</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="text-xl font-bold text-blue-800 mb-4">Dimensions & Form Factor</h3>
            <p className="text-blue-700 mb-4">
              The EH 11 Series offers a smaller footprint than traditional tower UPS systems of similar capacity, making it ideal for environments where space is at a premium while still providing robust power protection.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-600">
                <div className="font-bold">Dimensions (W×D×H)</div>
                <div>EH 11 - 6K: 190 × 468 × 720 mm</div>
                <div>EH 11 - 10K: 231 × 640 × 750 mm</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Application Areas */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-blue-900 mb-3 inline-block relative">
              Ideal Applications
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-6 text-xl text-blue-700 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Perfect solutions for these critical environments
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {[
            { icon: "🏢", text: "Financial Services" },
            { icon: "🏥", text: "Medical Facilities" },
            { icon: "💻", text: "Network Infrastructure" },
            { icon: "🏭", text: "Industrial Control" }
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-blue-50 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-blue-100/50"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: idx * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="text-3xl mb-3"
                animate={{
                  y: [0, -5, 0],
                  rotate: [-2, 2, -2]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                {item.icon}
              </motion.div>
              <p className="text-blue-700 font-medium">{item.text}</p>
            </motion.div>
          ))}
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.05, backgroundColor: "#f0f9ff" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-6 shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-blue-800 mb-4">Financial Services</h3>
            <p className="text-blue-700">
              Protects critical financial systems like ATMs, trading workstations, and branch office servers with reliable backup power.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.05, backgroundColor: "#f0f9ff" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-6 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-blue-800 mb-4">Medical Facilities</h3>
            <p className="text-blue-700">
              Provides reliable power for diagnostic equipment, administration systems, and clinical workstations in healthcare environments.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.05, backgroundColor: "#f0f9ff" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-6 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-blue-800 mb-4">Network Infrastructure</h3>
            <p className="text-blue-700">
              Ensures continuous operation of servers, switches, routers, and network attached storage devices with clean, reliable power during outages.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Modern EH 11 Series Comparison */}
      <section className="max-w-7xl mx-auto px-4 mb-20 relative">

        <motion.div
          className="text-center mb-12 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-blue-900 mb-3 inline-block relative">
              Why Choose EH 11 Series
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-6 text-xl text-blue-700 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Compelling reasons to select our premium UPS solution
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative z-10">
          {/* Technical Advantages Card */}
          <motion.div
            className="bg-white rounded-2xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, x: -50, y: 20 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              delay: 0.2
            }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -5 }}
          >
            {/* Top gradient bar */}
            <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-700"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-6 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-12 -right-12 w-40 h-40 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-r from-blue-700/20 to-transparent"></div>

              <div className="flex items-center relative z-10">
                <motion.div
                  className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center shadow-lg mr-4"
                  whileHover={{ rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 10 }}
                >
                  <Shield className="h-8 w-8 text-blue-600" />
                </motion.div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Technical Advantages</h3>
                  <p className="text-blue-100">Superior engineering features</p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="p-6">
              <ul className="space-y-4">
                {[
                  "Unity power factor (1.0) provides full rated power capacity",
                  "Advanced DSP technology enables flexible operation and superior control",
                  "Wide input voltage range (115-280 VAC) handles unstable power conditions",
                  "Tool-free maintenance simplifies service and reduces downtime",
                  "Three-level design with self-protection improves reliability"
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="flex-shrink-0 mt-1 mr-3 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center"
                      whileHover={{ scale: 1.2, rotate: 10 }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      <Check size={14} />
                    </motion.div>
                    <span className="text-blue-700">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>

          {/* Business Benefits Card */}
          <motion.div
            className="bg-white rounded-2xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, x: 50, y: 20 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              delay: 0.3
            }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -5 }}
          >
            {/* Top gradient bar */}
            <div className="h-2 bg-gradient-to-r from-blue-600 to-blue-800"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-6 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-12 -right-12 w-40 h-40 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-r from-blue-800/20 to-transparent"></div>

              <div className="flex items-center relative z-10">
                <motion.div
                  className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center shadow-lg mr-4"
                  whileHover={{ rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 10 }}
                >
                  <BarChart3 className="h-8 w-8 text-blue-700" />
                </motion.div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Business Benefits</h3>
                  <p className="text-blue-100">Operational advantages</p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="p-6">
              <ul className="space-y-4">
                {[
                  "Smaller footprint saves valuable floor space in crowded IT environments",
                  "Lower total cost of ownership through high efficiency operation (up to 95%)",
                  "Reduced energy consumption and heat output saves on cooling costs",
                  "Scalable design allows for future expansion as power needs grow",
                  "Advanced protection features safeguard critical equipment investments"
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="flex-shrink-0 mt-1 mr-3 w-6 h-6 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center"
                      whileHover={{ scale: 1.2, rotate: 10 }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      <Check size={14} />
                    </motion.div>
                    <span className="text-blue-700">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Installation and Setup */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl font-bold text-blue-900 mb-3">Easy Installation and Setup</h2>
          <div className="h-1.5 w-32 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto">
            Simple steps to get your UPS up and running quickly
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 bg-gradient-to-br from-blue-50 to-white rounded-3xl py-12 px-8">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-blue-700 rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">1</div>
              <h3 className="text-xl font-bold text-blue-800 mb-4">Placement</h3>
              <p className="text-blue-700">
                Install in a clean, stable environment with adequate ventilation, at least 20cm from walls or other equipment.
              </p>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-blue-700 rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">2</div>
              <h3 className="text-xl font-bold text-blue-800 mb-4">Connection</h3>
              <p className="text-blue-700">
                Connect input power and equipment loads using appropriate gauge cables, ensuring proper grounding for safety.
              </p>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-blue-700 rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">3</div>
              <h3 className="text-xl font-bold text-blue-800 mb-4">Configuration</h3>
              <p className="text-blue-700">
                Use the LCD display to set voltage, frequency, and operation mode preferences for your specific power environment.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Need More Information Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="relative"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <div className="bg-blue-50 rounded-xl p-10 text-center shadow-sm overflow-hidden">
            <div className="relative z-10">
              <motion.h2
                className="text-3xl font-bold mb-4 text-blue-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                viewport={{ once: true }}
              >
                Need More Information?
              </motion.h2>

              <motion.p
                className="text-lg mb-8 max-w-3xl mx-auto text-blue-600"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and
                any other details you need about the KRYKARD UPS systems.
              </motion.p>

              <motion.div
                className="flex justify-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <motion.a
                  href="/contact/sales"
                  className="bg-blue-600 text-white hover:bg-blue-700 shadow-md transition-all duration-300 text-base px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2 group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>Contact Our Experts</span>
                </motion.a>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </section>
    </div>
  );

  // Return PageLayout component with the product specification content inside
  return (
    <PageLayout
      title="KRYKARD EH 11 Series UPS"
      subtitle="Smaller footprint with robust backup solutions"
      category="protect"
      image="/background_images/ups_layout.png"
    >
      <ProductSpecContent />
    </PageLayout>
  );
};

export default ProductSpecification;