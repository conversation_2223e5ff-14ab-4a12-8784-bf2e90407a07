import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  CheckCircle2,
  Clock,
  Zap,
  Shield,
  Lightbulb,
  BarChart3
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import PageLayout from "@/components/layout/PageLayout";

// PDF Viewer Component
const PdfViewer = ({ showPdfViewer, setShowPdfViewer, pdfUrl, title }) => {
  // Function to directly download the PDF without any dialog
  const handleDownloadPdf = () => {
    // Create a hidden anchor element
    const a = document.createElement('a');

    // Set direct download attributes
    a.style.display = 'none';
    a.href = pdfUrl;
    a.setAttribute('download', 'KRYKARD-LES-Brochure.pdf');

    // Append to body
    document.body.appendChild(a);

    // Trigger click programmatically
    a.click();

    // Remove element after download is triggered
    document.body.removeChild(a);

    // Force a download by opening in a new window as backup
    if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident/') !== -1) {
      window.open(pdfUrl, '_blank');
    }
  };

  // Function to open PDF in a new tab
  const handleOpenInNewTab = () => {
    window.open(pdfUrl, '_blank');
  };

  // Animation variants for modal
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    }
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${showPdfViewer ? '' : 'hidden'}`}>
      {/* Backdrop with blur effect */}
      <div
        className="absolute inset-0 bg-black bg-opacity-70 backdrop-blur-sm"
        onClick={() => setShowPdfViewer(false)}
      ></div>

      {/* Modal content */}
      <motion.div
        className="relative bg-white dark:bg-slate-800 rounded-xl p-6 w-full max-w-5xl max-h-[90vh] overflow-hidden shadow-2xl border border-slate-200 dark:border-slate-700"
        initial="hidden"
        animate={showPdfViewer ? "visible" : "hidden"}
        exit="exit"
        variants={modalVariants}
      >
        {/* Close button with enhanced styling */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 bg-white dark:bg-slate-700 rounded-full p-1 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 z-10"
          onClick={() => setShowPdfViewer(false)}
          aria-label="Close PDF viewer"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Header with title and actions */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 pb-4 border-b border-slate-200 dark:border-slate-700">
          <h3 className="text-xl font-bold text-green-800 dark:text-green-400 mb-3 sm:mb-0">{title || "KRYKARD Lighting Energy Saver Brochure"}</h3>
          <div className="flex items-center gap-3">
            <button
              onClick={handleOpenInNewTab}
              className="flex items-center gap-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 py-2 px-4 rounded-md transition-colors"
              aria-label="Open PDF in new tab"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              <span className="hidden sm:inline">Open in New Tab</span>
            </button>
            <button
              onClick={handleDownloadPdf}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md transition-colors"
              aria-label="Download PDF"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              <span className="hidden sm:inline">Download PDF</span>
            </button>
          </div>
        </div>

        {/* PDF content with loading indicator */}
        <div className="w-full h-[70vh] relative">
          {/* Loading indicator */}
          <div className="absolute inset-0 flex items-center justify-center bg-slate-50 dark:bg-slate-800 z-0">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-slate-600 dark:text-slate-300">Loading PDF...</p>
            </div>
          </div>

          {/* Direct PDF embedding */}
          <object
            data={pdfUrl}
            type="application/pdf"
            className="w-full h-full relative z-10"
            width="100%"
            height="100%"
          >
            <div className="flex flex-col items-center justify-center h-full bg-slate-100 dark:bg-slate-700 rounded-lg p-8 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p className="text-slate-700 dark:text-slate-200 text-lg font-medium mb-2">
                PDF preview is not available in your browser
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                You can download the PDF or open it in a new tab instead
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={handleOpenInNewTab}
                  className="flex items-center justify-center gap-2 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 py-3 px-6 rounded-lg transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Open in New Tab
                </button>
                <button
                  onClick={handleDownloadPdf}
                  className="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download PDF
                </button>
              </div>
            </div>
          </object>
        </div>
      </motion.div>
    </div>
  );
};

// Animated energy particles effect (2D version using CSS)
const EnergyParticles = ({ count = 20, color = "green" }) => {
  const colorMap = {
    amber: "bg-amber-300",
    blue: "bg-blue-300",
    green: "bg-green-300",
    purple: "bg-purple-300"
  };

  const bgColor = colorMap[color] || colorMap.green;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-2 h-2 rounded-full ${bgColor}`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            opacity: [0.7, 0.1, 0.7],
            scale: [1, Math.random() + 0.5, 1],
          }}
          transition={{
            duration: 3 + Math.random() * 5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Feature card component with hover effects
const FeatureCard = ({ icon, title, description }) => {
  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
    >
      <div className="px-6 py-8">
        <div className="w-14 h-14 rounded-full bg-green-100 mb-6 flex items-center justify-center">
          {icon}
        </div>
        <h3 className="text-xl font-bold mb-3 text-gray-900">{title}</h3>
        <p className="text-gray-800 leading-relaxed">{description}</p>
      </div>
      <div className="h-1 bg-gradient-to-r from-green-400 to-green-600"></div>
    </motion.div>
  );
};

// Enhanced animated counter component with better visuals
const AnimatedCounter = ({ value, title, icon, suffix = "", color = "green" }) => {
  const [count, setCount] = useState(0);
  const [inView, setInView] = useState(false);
  const counterRef = useRef(null);

  const colorMap = {
    amber: {
      bg: "bg-gradient-to-br from-amber-50 to-amber-100",
      iconBg: "bg-amber-100",
      iconColor: "text-amber-500",
      textColor: "text-amber-700"
    },
    blue: {
      bg: "bg-gradient-to-br from-blue-50 to-blue-100",
      iconBg: "bg-blue-100",
      iconColor: "text-blue-500",
      textColor: "text-blue-700"
    },
    green: {
      bg: "bg-gradient-to-br from-green-50 to-green-100",
      iconBg: "bg-green-100",
      iconColor: "text-green-500",
      textColor: "text-green-700"
    },
    purple: {
      bg: "bg-gradient-to-br from-purple-50 to-purple-100",
      iconBg: "bg-purple-100",
      iconColor: "text-purple-500",
      textColor: "text-purple-700"
    }
  };

  const theme = colorMap[color] || colorMap.green;

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => {
      if (counterRef.current) {
        observer.unobserve(counterRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (inView) {
      let start = 0;
      const end = parseInt(value);
      const duration = 2000;
      const increment = end / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start > end) {
          setCount(end);
          clearInterval(timer);
        } else {
          setCount(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(timer);
    }
  }, [inView, value]);

  return (
    <motion.div
      ref={counterRef}
      className={`rounded-xl p-8 shadow-xl overflow-hidden relative ${theme.bg}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7 }}
      viewport={{ once: true }}
      whileHover={{
        scale: 1.02,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
      }}
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-white opacity-10 rounded-full transform translate-x-8 -translate-y-8"></div>
      <div className="absolute bottom-0 left-0 w-20 h-20 bg-white opacity-10 rounded-full transform -translate-x-10 translate-y-10"></div>

      <div className="text-center relative z-10">
        <div className={`w-16 h-16 rounded-full ${theme.iconBg} flex items-center justify-center mb-5 mx-auto shadow-md`}>
          {React.cloneElement(icon, { className: `h-8 w-8 ${theme.iconColor}` })}
        </div>

        <div className="relative">
          <motion.h3
            className={`text-5xl font-bold ${theme.textColor} mb-1 tracking-tight`}
            initial={{ scale: 0.9 }}
            animate={inView ? { scale: [0.9, 1.1, 1] } : {}}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {count}{suffix}
          </motion.h3>

          {/* Decorative line */}
          <div className="h-1 w-12 bg-white/50 mx-auto my-3 rounded-full"></div>

          <p className="text-gray-800 font-medium">{title}</p>
        </div>
      </div>
    </motion.div>
  );
};

// Main component
const LightingEnergySaver = () => {
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [isConceptInView, setIsConceptInView] = useState(false);
  const [isFeaturesInView, setIsFeaturesInView] = useState(false);
  const conceptRef = useRef(null);
  const featuresRef = useRef(null);

  // Function to handle brochure button click
  const handleBrochureClick = () => {
    setShowPdfViewer(true);
  };

  useEffect(() => {
    const conceptObserver = new IntersectionObserver(
      ([entry]) => {
        setIsConceptInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    const featuresObserver = new IntersectionObserver(
      ([entry]) => {
        setIsFeaturesInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (conceptRef.current) conceptObserver.observe(conceptRef.current);
    if (featuresRef.current) featuresObserver.observe(featuresRef.current);

    return () => {
      if (conceptRef.current) conceptObserver.unobserve(conceptRef.current);
      if (featuresRef.current) featuresObserver.unobserve(featuresRef.current);
    };
  }, []);

  const features = [
    {
      icon: <Zap className="h-6 w-6 text-green-500" />,
      title: "Energy Efficient",
      description: "Saves up to 35% energy consumption while maintaining optimal lighting levels for productivity."
    },
    {
      icon: <Clock className="h-6 w-6 text-green-500" />,
      title: "Rapid ROI",
      description: "Achieve payback in as little as 9-12 months with immediate energy cost reduction."
    },
    {
      icon: <Shield className="h-6 w-6 text-green-500" />,
      title: "Extended Lifespan",
      description: "Increases lamp life by reducing voltage stress and minimizing UV/IR emissions."
    },
    {
      icon: <Lightbulb className="h-6 w-6 text-green-500" />,
      title: "Consistent Lighting",
      description: "Maintains consistent illumination levels while optimizing energy consumption."
    },
    {
      icon: <BarChart3 className="h-6 w-6 text-green-500" />,
      title: "Smart Controller",
      description: "Intelligent system with multi-level time and voltage settings for maximum efficiency."
    },
    {
      icon: <CheckCircle2 className="h-6 w-6 text-green-500" />,
      title: "Universal Compatibility",
      description: "Works with CFL, LED, Halogen, and traditional lighting systems without modification."
    }
  ];

  const specifications = [
    {
      category: "Input",
      items: [
        { label: "Voltage", value: "150-270V AC" },
        { label: "Frequency", value: "50Hz ±5%" },
        { label: "Phase", value: "Single Phase" }
      ]
    },
    {
      category: "Output",
      items: [
        { label: "Voltage Range", value: "Programmable 160-240V AC" },
        { label: "Regulation", value: "±1%" },
        { label: "Protection", value: "Overload, Short Circuit" }
      ]
    },
    {
      category: "Mechanical",
      items: [
        { label: "Enclosure", value: "CRCA Powder Coated" },
        { label: "Protection Class", value: "IP54" },
        { label: "Operating Temperature", value: "0-45°C" }
      ]
    }
  ];

  return (
    <PageLayout
      title="Lighting Energy Saver"
      subtitle="Next-generation efficiency for modern energy conservation"
      category="conserve"
    >
      {/* Hero Section - Enhanced with Parallax and Glassmorphism */}
      <section className="py-20 mb-0 relative overflow-hidden">
        {/* Dynamic background with moving gradients */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/30 dark:via-emerald-900/30 dark:to-teal-900/30"></div>
          <motion.div
            className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.3),transparent_70%)]"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.2, 0.3, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          ></motion.div>
        </div>

        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">
            {/* Image on the left */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-6 relative order-2 lg:order-1"
            >
              {/* Enhanced decorative background elements to blend the image */}
              <div className="absolute -inset-4 bg-gradient-to-br from-green-50/40 to-transparent rounded-full blur-2xl"></div>
              <div className="absolute -bottom-10 -left-10 w-60 h-60 bg-green-100/30 rounded-full blur-3xl"></div>
              <div className="absolute -top-10 -right-10 w-60 h-60 bg-green-100/20 rounded-full blur-3xl"></div>
              <div className="absolute inset-0 bg-gradient-to-tr from-green-50/10 via-transparent to-green-50/10 rounded-full"></div>

              {/* Additional subtle glow elements */}
              <motion.div
                className="absolute top-1/4 left-1/4 w-20 h-20 bg-green-300/20 rounded-full blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.2, 0.3, 0.2]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              ></motion.div>

              {/* Image with floating effect and no box layout */}
              <motion.div
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 0.5, 0]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
                className="relative z-10"
              >
                <img
                  src="/les.png"
                  alt="KRYKARD Lighting Energy Saver"
                  className="w-full h-auto object-contain filter drop-shadow-2xl"
                  style={{ maxHeight: "600px" }}
                />

                Enhanced subtle glow effect
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-green-400/10 to-transparent"></div>

                {/* Light reflection effect */}
                <motion.div
                  className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 via-transparent to-transparent"
                  animate={{
                    opacity: [0.1, 0.2, 0.1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
              </motion.div>
            </motion.div>

            {/* Content on the right */}
            <motion.div
              className="lg:col-span-6 order-1 lg:order-2"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, ease: "easeOut" }}
                className="relative"
              >
                {/* Decorative element */}
                <div className="absolute -right-6 -top-6 w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-emerald-300 opacity-20 blur-2xl"></div>

                <h1 className="text-5xl font-bold mb-6 leading-tight bg-clip-text text-transparent bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 dark:from-green-400 dark:via-emerald-400 dark:to-teal-400">
                  Payback in as low as <span className="relative inline-block">
                    9 months!
                    <svg className="absolute -bottom-2 left-0 w-full h-2 text-emerald-400/30" viewBox="0 0 100 12" preserveAspectRatio="none">
                      <path d="M0,0 Q50,12 100,0" stroke="currentColor" strokeWidth="8" fill="none" />
                    </svg>
                  </span>
                </h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
                  className="text-slate-700 dark:text-slate-200 text-xl mb-8 leading-relaxed font-medium"
                >
                  Depreciation is an added benefit.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.3, ease: "easeOut" }}
                  className="mb-8"
                >
                  <h2 className="text-3xl font-bold mb-6 text-slate-800 dark:text-slate-100">
                    Applications
                  </h2>
                  <ul className="space-y-4">
                    <li className="flex items-center">
                      <div className="rounded-full bg-yellow-400 p-1 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-slate-700 dark:text-slate-200">Large Offices – particularly Software & BPO facilities</span>
                    </li>
                    <li className="flex items-center">
                      <div className="rounded-full bg-yellow-400 p-1 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-slate-700 dark:text-slate-200">Shop floor lighting in Industries, particularly those that are dependent largely on artificial lighting</span>
                    </li>
                    <li className="flex items-center">
                      <div className="rounded-full bg-yellow-400 p-1 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-slate-700 dark:text-slate-200">Hospitals, Educational Institutes & Research laboratories</span>
                    </li>
                    <li className="flex items-center">
                      <div className="rounded-full bg-yellow-400 p-1 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-slate-700 dark:text-slate-200">Commercial & Residential complexes</span>
                    </li>
                    <li className="flex items-center">
                      <div className="rounded-full bg-yellow-400 p-1 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-slate-700 dark:text-slate-200">Campus Lighting</span>
                    </li>
                  </ul>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
                  className="flex flex-wrap gap-5"
                >
                  <Button
                    className="bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 text-white shadow-lg font-semibold text-base px-8 py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1"
                    style={{ boxShadow: "0 10px 30px -5px rgba(16, 185, 129, 0.5)" }}
                    onClick={() => window.location.href = "/contact/sales"}
                  >
                    <span className="mr-2">GET A QUOTE</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </Button>

                  <Button
                    variant="outline"
                    className="border-2 border-green-500 text-green-600 hover:bg-green-50 dark:text-green-300 dark:border-green-700 dark:hover:bg-green-900/30 px-8 py-6 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1 relative group"
                    onClick={handleBrochureClick}
                  >
                    {/* Add a subtle glow effect on hover */}
                    <div className="absolute -inset-1 bg-green-500/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center">
                      <span className="mr-2">VIEW PDF BROCHURE</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 2 0 002 2h8a2 2 2 0 002-2V7.414A2 2 2 0 0015.414 6L12 2.586A2 2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Statistics Section with animated counters */}
      <section className="container mx-auto px-4 mb-24">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-3">Key Benefits</h2>
          <div className="h-1.5 w-24 bg-gradient-to-r from-green-400 to-green-600 mx-auto rounded-full"></div>
          <p className="mt-6 text-lg text-gray-700 max-w-2xl mx-auto">
            Our energy-saving technology delivers multiple advantages for your lighting systems
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <AnimatedCounter
            value="35"
            suffix="%"
            title="Energy Savings"
            icon={<Zap className="h-8 w-8" />}
            color="green"
          />
          <AnimatedCounter
            value="9"
            suffix=""
            title="Month Payback"
            icon={<Clock className="h-8 w-8" />}
            color="green"
          />
          <AnimatedCounter
            value="30"
            suffix="%"
            title="Reduced Lighting Costs"
            icon={<Lightbulb className="h-8 w-8" />}
            color="green"
          />
          <AnimatedCounter
            value="50"
            suffix="%"
            title="Extended Lamp Life"
            icon={<Shield className="h-8 w-8" />}
            color="green"
          />
        </div>
      </section>

      {/* LES Concept Section - Removed box layout and blended with UI */}
      <section
        ref={conceptRef}
        className="relative py-24 mb-24 overflow-hidden bg-gradient-to-r from-green-400 via-green-500 to-green-600"
      >
        {/* Background shadow effects */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute -right-20 -top-20 w-96 h-96 bg-green-300 opacity-20 rounded-full blur-3xl shadow-2xl"
            animate={{
              scale: [1, 1.2, 1],
              x: [0, 20, 0],
              y: [0, -20, 0]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute -left-20 -bottom-20 w-96 h-96 bg-green-300 opacity-20 rounded-full blur-3xl shadow-2xl"
            animate={{
              scale: [1, 1.3, 1],
              x: [0, -20, 0],
              y: [0, 20, 0]
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </div>

        {/* Animated particles */}
        <EnergyParticles count={15} color="green" />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 40 }}
            animate={isConceptInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
              <div>
                <motion.h2
                  className="text-4xl font-bold mb-6 text-white drop-shadow-md"
                  initial={{ opacity: 0, x: -20 }}
                  animate={isConceptInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  The LES Concept
                </motion.h2>
                <motion.div
                  className="h-1.5 w-20 bg-white rounded mb-6"
                  initial={{ width: 0 }}
                  animate={isConceptInView ? { width: 80 } : {}}
                  transition={{ duration: 0.8, delay: 0.4 }}
                />
                <motion.p
                  className="text-xl text-white/90 font-medium drop-shadow-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isConceptInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  Transform energy consumption without complex installations or major investments.
                </motion.p>
              </div>

              <div className="lg:col-span-2 space-y-6">
                <motion.p
                  className="text-white text-lg leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isConceptInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  LES offers a comprehensive solution to control voltage across various lighting types — CFL, Halogen, electronic chokes, and more — maximizing energy efficiency.
                </motion.p>
                <motion.p
                  className="text-white text-lg leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isConceptInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.5 }}
                >
                  Our intelligent system dynamically maintains optimal power delivery to ensure energy savings without compromising performance or visual comfort.
                </motion.p>
                <motion.p
                  className="text-white text-lg leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={isConceptInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  By reducing excess voltage consumption, LES helps in lowering overall electricity costs while enhancing lamp longevity, creating a truly sustainable lighting solution.
                </motion.p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features & Benefits Section with interactive cards */}
      <section
        ref={featuresRef}
        className="container mx-auto px-4 mb-24"
      >
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={isFeaturesInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-3">Features & Benefits</h2>
          <div className="h-1.5 w-24 bg-gradient-to-r from-green-400 to-green-600 mx-auto rounded-full"></div>
          <p className="text-xl text-gray-700 mt-6 max-w-2xl mx-auto">
            Our lighting energy saver provides comprehensive benefits with cutting-edge technology
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </section>

      {/* Enhanced Technical Specifications Section - Redesigned to match UI */}
      <section className="relative py-20 mb-24 overflow-hidden">
        {/* Gradient background with smooth transitions */}
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100 via-white to-gray-100"></div>

        {/* Background shadow effects */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute right-0 top-0 w-80 h-80 bg-green-200 opacity-20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.1, 0.2, 0.1]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute left-0 bottom-0 w-80 h-80 bg-green-200 opacity-15 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.1, 0.15, 0.1]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-3">Technical Specifications</h2>
            <div className="h-1.5 w-32 bg-gradient-to-r from-green-400 to-green-600 mx-auto rounded-full"></div>
            <p className="text-lg text-gray-700 mt-6 max-w-2xl mx-auto">
              Engineered for optimal performance across a wide range of lighting applications
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Category heading */}
                <h3 className="text-2xl font-bold mb-6 text-gray-900 inline-block">
                  {spec.category}
                  <div className="absolute h-1 w-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full bottom-0 left-0 transform translate-y-2"></div>
                </h3>

                {/* Specs list with subtle separators */}
                <ul className="space-y-4 mt-8">
                  {spec.items.map((item, itemIndex) => (
                    <motion.li
                      key={itemIndex}
                      className="flex justify-between items-center border-b border-gray-200 pb-3"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <span className="text-gray-700 font-medium">{item.label}</span>
                      <span className="font-bold text-gray-900">{item.value}</span>
                    </motion.li>
                  ))}
                </ul>

                {/* Decorative corner accent */}
                <motion.div
                  className="absolute -bottom-2 -right-2 w-20 h-20 border-r-2 border-b-2 border-green-300 rounded-br-xl opacity-30"
                  animate={{
                    opacity: [0.3, 0.5, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Contact Section with Green Theme */}
      <section className="py-24 relative overflow-hidden">
        {/* Stronger green gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-green-50/80 to-emerald-100/60 dark:from-green-900/40 dark:via-green-800/30 dark:to-emerald-900/20 -z-10"></div>

        {/* Enhanced animated dots background with more green */}
        <motion.div
          className="absolute inset-0 -z-10 opacity-15"
          animate={{
            backgroundPosition: ['0% 0%', '100% 100%'],
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 20,
            ease: "linear"
          }}
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23059669" fill-opacity="0.4"%3E%3Ccircle cx="10" cy="10" r="2"/%3E%3C/g%3E%3C/svg%3E")'
          }}
        />

        <div className="container mx-auto px-4 max-w-6xl">
          {/* Need More Information Card with enhanced green styling */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="rounded-3xl p-12 relative bg-gradient-to-br from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/20 backdrop-blur-md border border-green-200 dark:border-green-700/40 shadow-2xl overflow-hidden"
          >
            {/* Enhanced decorative elements with more vibrant green */}
            <div className="absolute -top-20 -right-20 w-60 h-60 bg-green-500/15 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-60 h-60 bg-green-500/15 rounded-full blur-3xl"></div>

            {/* Additional decorative elements */}
            <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-green-400/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-1/3 right-1/4 w-32 h-32 bg-green-400/10 rounded-full blur-2xl"></div>

            <div className="relative z-10 text-center max-w-3xl mx-auto">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-4xl font-bold mb-6 text-green-800 dark:text-green-300"
              >
                Need More Information?
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-lg mb-10 text-green-700 dark:text-green-200"
              >
                Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other
                details you need about <span className="font-semibold text-green-600 dark:text-green-400">KRYKARD Lighting Energy Saver</span>.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex justify-center"
              >
                <motion.button
                  whileHover={{ scale: 1.05, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                  className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white font-semibold py-4 px-10 rounded-xl flex items-center gap-3 shadow-xl"
                  style={{ boxShadow: "0 10px 30px rgba(22, 163, 74, 0.4)" }}
                  onClick={() => window.location.href = "/contact/sales"}
                >
                  <span>Contact Our Experts</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </motion.button>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* PDF Viewer Modal */}
      <PdfViewer
        showPdfViewer={showPdfViewer}
        setShowPdfViewer={setShowPdfViewer}
        pdfUrl="/KRYKARD-Comprehensive-Product-Catalogue.pdf"
        title="KRYKARD Product Catalogue"
      />
    </PageLayout>
  );
};

export default LightingEnergySaver;